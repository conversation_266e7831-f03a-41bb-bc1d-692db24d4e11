// Comprehensive test for Gemini's coding capabilities
import dotenv from 'dotenv';
import { getGeminiResponse } from './utils/google-ai.js';

dotenv.config();

async function testGeminiCoding() {
  const apiKey = process.env.GOOGLE_AI_API_KEY;
  
  if (!apiKey) {
    console.error('❌ GOOGLE_AI_API_KEY not found in .env file');
    return;
  }
  
  console.log('🔑 API Key found:', apiKey.substring(0, 10) + '...');
  
  const codingTests = [
    {
      name: "🎯 JavaScript ES6+ Features",
      prompt: "Create a modern JavaScript application using ES6+ features: classes, arrow functions, destructuring, async/await, modules, and template literals. Build a simple task manager with local storage."
    },
    {
      name: "📚 External Libraries Integration",
      prompt: "Create a complete HTML page that integrates multiple external libraries via CDN: Bootstrap 5, Font Awesome, Chart.js, and AOS (Animate On Scroll). Build a dashboard with animated charts and icons."
    },
    {
      name: "🎨 CSS Frameworks Integration",
      prompt: "Create a landing page using Tailwind CSS via CDN. Include responsive design, dark mode toggle, and custom animations. Use Tailwind's utility classes extensively."
    },
    {
      name: "📊 Data Visualization",
      prompt: "Create an interactive data visualization dashboard using Chart.js and D3.js via CDN. Include multiple chart types: line, bar, pie, and real-time updating charts."
    },
    {
      name: "🎮 Interactive Components",
      prompt: "Create interactive components using vanilla JavaScript: image carousel, modal dialogs, dropdown menus, form validation, and drag-and-drop functionality."
    },
    {
      name: "🌐 API Integration",
      prompt: "Create a weather app that fetches data from a public API, handles errors gracefully, shows loading states, and displays data in a beautiful UI with icons and animations."
    },
    {
      name: "📱 Progressive Web App",
      prompt: "Create a PWA (Progressive Web App) with service worker, offline functionality, installable manifest, and responsive design. Include push notifications setup."
    },
    {
      name: "🎭 Animation Libraries",
      prompt: "Create an animated landing page using GSAP (GreenSock) and Lottie animations via CDN. Include scroll-triggered animations, timeline animations, and interactive elements."
    }
  ];

  console.log('\n🚀 Starting Comprehensive Coding Tests...\n');

  for (let i = 0; i < codingTests.length; i++) {
    const test = codingTests[i];
    console.log(`${test.name}`);
    console.log(`📝 Test ${i + 1}/${codingTests.length}: ${test.name.replace(/🎯|📚|🎨|📊|🎮|🌐|📱|🎭/g, '').trim()}`);
    
    const messages = [
      {
        role: "system",
        content: `You are WIDDX DEV AI, an expert full-stack developer specialized in creating modern, production-ready web applications.

CORE PRINCIPLES:
- Always create complete, working HTML files
- Use modern JavaScript (ES6+) features
- Integrate external libraries via CDN when requested
- Include proper error handling and loading states
- Ensure responsive design and accessibility
- Add comprehensive comments explaining the code
- Follow best practices for performance and SEO
- Create production-ready, professional code

EXTERNAL LIBRARIES INTEGRATION:
- Always use latest stable versions from CDN
- Include proper integrity checks when available
- Handle library loading errors gracefully
- Use libraries efficiently and follow their best practices
- Combine multiple libraries seamlessly when needed

CODE QUALITY REQUIREMENTS:
- Clean, readable, and well-organized code
- Proper separation of concerns (HTML, CSS, JS)
- Modern JavaScript features and patterns
- Responsive design with mobile-first approach
- Accessibility features (ARIA labels, semantic HTML)
- Performance optimizations
- Cross-browser compatibility

ALWAYS create something unique, modern, and production-ready that represents the quality of WIDDX hosting services.`
      },
      {
        role: "user",
        content: test.prompt
      }
    ];

    try {
      const response = await getGeminiResponse(apiKey, 'gemini-1.5-flash', messages, 8192, true);
      const content = response.choices[0]?.message?.content;
      
      if (content) {
        // Comprehensive analysis
        const analysis = analyzeCode(content);
        
        console.log(`   ✅ Response generated (${content.length} chars)`);
        console.log(`   🧠 Thinking: ${analysis.hasThinking ? '✅' : '❌'}`);
        console.log(`   📄 HTML Structure: ${analysis.hasHTML ? '✅' : '❌'}`);
        console.log(`   🎨 CSS Styling: ${analysis.hasCSS ? '✅' : '❌'}`);
        console.log(`   ⚡ JavaScript: ${analysis.hasJS ? '✅' : '❌'}`);
        console.log(`   🔗 External CDN: ${analysis.hasCDN ? '✅' : '❌'}`);
        console.log(`   📱 Responsive: ${analysis.isResponsive ? '✅' : '❌'}`);
        console.log(`   ♿ Accessibility: ${analysis.hasAccessibility ? '✅' : '❌'}`);
        console.log(`   💬 Comments: ${analysis.commentCount} found`);
        console.log(`   🆕 Modern JS: ${analysis.hasModernJS ? '✅' : '❌'}`);
        console.log(`   🎯 Error Handling: ${analysis.hasErrorHandling ? '✅' : '❌'}`);
        
        if (analysis.cdnLibraries.length > 0) {
          console.log(`   📚 CDN Libraries: ${analysis.cdnLibraries.join(', ')}`);
        }
        
        if (analysis.jsFeatures.length > 0) {
          console.log(`   🔧 JS Features: ${analysis.jsFeatures.join(', ')}`);
        }
        
        // Show code snippet
        const snippet = getCodeSnippet(content);
        if (snippet) {
          console.log(`   📋 Code Preview:\n${snippet}`);
        }
        
      } else {
        console.log('   ❌ No content returned');
      }
      
    } catch (error) {
      console.log(`   ❌ Error: ${error.message}`);
    }
    
    console.log('\n' + '='.repeat(80) + '\n');
    
    // Add delay to avoid rate limiting
    if (i < codingTests.length - 1) {
      console.log('⏳ Waiting 3 seconds to avoid rate limiting...\n');
      await new Promise(resolve => setTimeout(resolve, 3000));
    }
  }
}

function analyzeCode(content) {
  const analysis = {
    hasThinking: content.includes('<think>') && content.includes('</think>'),
    hasHTML: content.includes('<!DOCTYPE html>') || content.includes('<html'),
    hasCSS: content.includes('<style>') || content.includes('.css') || (content.includes('.') && content.includes('{')),
    hasJS: content.includes('<script>') || content.includes('function') || content.includes('=>'),
    hasCDN: content.includes('cdn.') || content.includes('unpkg.') || content.includes('jsdelivr.'),
    isResponsive: content.includes('@media') || content.includes('responsive') || content.includes('viewport'),
    hasAccessibility: content.includes('aria-') || content.includes('alt=') || content.includes('role='),
    commentCount: (content.match(/\/\*[\s\S]*?\*\/|\/\/.*$/gm) || []).length,
    hasModernJS: false,
    hasErrorHandling: content.includes('try') || content.includes('catch') || content.includes('error'),
    cdnLibraries: [],
    jsFeatures: []
  };

  // Detect CDN libraries
  const cdnPatterns = [
    { name: 'Bootstrap', pattern: /bootstrap/i },
    { name: 'Font Awesome', pattern: /font-?awesome/i },
    { name: 'Chart.js', pattern: /chart\.js/i },
    { name: 'D3.js', pattern: /d3\.js|d3\.min/i },
    { name: 'GSAP', pattern: /gsap/i },
    { name: 'Tailwind', pattern: /tailwind/i },
    { name: 'AOS', pattern: /aos\.js|animate.*scroll/i },
    { name: 'Lottie', pattern: /lottie/i },
    { name: 'jQuery', pattern: /jquery/i },
    { name: 'Vue.js', pattern: /vue\.js/i },
    { name: 'React', pattern: /react\.js/i }
  ];

  cdnPatterns.forEach(lib => {
    if (lib.pattern.test(content)) {
      analysis.cdnLibraries.push(lib.name);
    }
  });

  // Detect modern JavaScript features
  const jsFeatures = [
    { name: 'Arrow Functions', pattern: /=>/g },
    { name: 'Async/Await', pattern: /async|await/g },
    { name: 'Destructuring', pattern: /const\s*{.*}|let\s*{.*}/g },
    { name: 'Template Literals', pattern: /`.*\${.*}.*`/g },
    { name: 'Classes', pattern: /class\s+\w+/g },
    { name: 'Modules', pattern: /import|export/g },
    { name: 'Promises', pattern: /\.then\(|\.catch\(|new Promise/g },
    { name: 'Fetch API', pattern: /fetch\(/g },
    { name: 'Spread Operator', pattern: /\.\.\./g },
    { name: 'const/let', pattern: /const\s+|let\s+/g }
  ];

  jsFeatures.forEach(feature => {
    if (feature.pattern.test(content)) {
      analysis.jsFeatures.push(feature.name);
      analysis.hasModernJS = true;
    }
  });

  return analysis;
}

function getCodeSnippet(content) {
  // Extract a meaningful code snippet
  const jsMatch = content.match(/<script>([\s\S]*?)<\/script>/);
  if (jsMatch) {
    const jsCode = jsMatch[1].trim();
    const lines = jsCode.split('\n').slice(0, 8);
    return lines.join('\n') + (jsCode.split('\n').length > 8 ? '\n...' : '');
  }
  
  const cssMatch = content.match(/<style>([\s\S]*?)<\/style>/);
  if (cssMatch) {
    const cssCode = cssMatch[1].trim();
    const lines = cssCode.split('\n').slice(0, 6);
    return lines.join('\n') + (cssCode.split('\n').length > 6 ? '\n...' : '');
  }
  
  return null;
}

async function runComprehensiveTests() {
  console.log('🔬 Starting Comprehensive Gemini Coding Tests\n');
  console.log('Testing: JavaScript, CSS, HTML, CDN Integration, Modern Features\n');
  
  await testGeminiCoding();
  
  console.log('✅ All comprehensive tests completed!');
  console.log('\n📊 Summary: Gemini has been tested for:');
  console.log('   🎯 Modern JavaScript (ES6+)');
  console.log('   📚 External Libraries via CDN');
  console.log('   🎨 CSS Frameworks Integration');
  console.log('   📊 Data Visualization');
  console.log('   🎮 Interactive Components');
  console.log('   🌐 API Integration');
  console.log('   📱 Progressive Web Apps');
  console.log('   🎭 Animation Libraries');
}

runComprehensiveTests().catch(console.error);
