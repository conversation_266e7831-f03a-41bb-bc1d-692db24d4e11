// Google AI (Gemini) integration for WIDDX DEV using REST API
// Based on the official Google AI REST API

// Rate limiting for Gemini API
let lastRequestTime = 0;
const MIN_REQUEST_INTERVAL = 3000; // 3 seconds between requests (conservative)

async function rateLimitedFetch(url, options) {
  const now = Date.now();
  const timeSinceLastRequest = now - lastRequestTime;

  if (timeSinceLastRequest < MIN_REQUEST_INTERVAL) {
    const delay = MIN_REQUEST_INTERVAL - timeSinceLastRequest;
    console.log(`[Gemini] Rate limiting: waiting ${delay}ms`);
    await new Promise(resolve => setTimeout(resolve, delay));
  }

  lastRequestTime = Date.now();
  return fetch(url, options);
}

/**
 * Get the appropriate Gemini model name for API
 */
function getGeminiModelName(modelName) {
  const modelMap = {
    'gemini-1.5-pro': 'gemini-1.5-pro',
    'gemini-1.5-flash': 'gemini-1.5-flash',
    'gemini-2.0-flash-exp': 'gemini-2.0-flash-exp'
  };

  return modelMap[modelName] || 'gemini-1.5-flash';
}

/**
 * Convert messages to Gemini format with thinking support
 */
function convertMessagesToGeminiFormat(messages, enableThinking = true) {
  const systemMessage = messages.find(m => m.role === 'system');
  const userMessages = messages.filter(m => m.role === 'user');

  // Enhanced system prompt with thinking instructions for Gemini
  let combinedText = '';
  if (systemMessage) {
    let systemContent = systemMessage.content;

    if (enableThinking) {
      systemContent += `\n\n[THINKING INSTRUCTIONS FOR GEMINI]
Before providing your final response, you MUST think through the problem step by step using <think> tags. This is required for all responses:

<think>
- Analyze the user's request carefully
- Consider the best approach for web development
- Think about modern best practices and responsive design
- Plan the HTML structure, CSS styling, and any JavaScript needed
- Consider accessibility, performance, and cross-browser compatibility
- Ensure the code follows WIDDX DEV standards
</think>

After your thinking process, provide your complete HTML response. Always include:
- Proper DOCTYPE declaration
- Complete head section with meta tags, title, and embedded CSS
- Full body structure with semantic HTML
- Responsive design using modern CSS
- Clean, well-commented code

Remember: You MUST include the <think> tags with your reasoning process before the HTML code.`;
    }

    combinedText += `${systemContent}\n\n`;
  }

  // Add user messages
  userMessages.forEach(msg => {
    combinedText += `${msg.content}\n`;
  });

  return {
    contents: [
      {
        parts: [
          {
            text: combinedText
          }
        ]
      }
    ]
  };
}

/**
 * Stream response from Gemini using REST API
 */
export async function* streamGeminiResponse(apiKey, modelName, messages, maxTokens = 8192, enableThinking = true) {
  try {
    const actualModelName = getGeminiModelName(modelName);
    const requestBody = convertMessagesToGeminiFormat(messages, enableThinking);

    // Add generation config optimized for code generation
    requestBody.generationConfig = {
      temperature: 0.7,
      topP: 0.8,
      topK: 40,
      maxOutputTokens: maxTokens,
      candidateCount: 1,
    };

    const url = `https://generativelanguage.googleapis.com/v1beta/models/${actualModelName}:streamGenerateContent?key=${apiKey}`;

    console.log('[Gemini] Starting stream request to:', url);
    console.log('[Gemini] Request body:', JSON.stringify(requestBody, null, 2));

    const response = await rateLimitedFetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody)
    });

    console.log('[Gemini] Response status:', response.status);

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      console.error('[Gemini] API Error:', errorData);

      if (response.status === 429) {
        throw new Error(`Gemini API rate limit exceeded. Please wait a few minutes and try again, or use another AI model like DeepSeek R1.`);
      }

      throw new Error(`Gemini API error: ${response.status} - ${errorData.error?.message || response.statusText}`);
    }

    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let buffer = '';
    let fullResponse = '';

    while (true) {
      const { done, value } = await reader.read();
      if (done) break;

      const chunk = decoder.decode(value, { stream: true });
      buffer += chunk;
      fullResponse += chunk;

      // Log raw chunk for debugging
      console.log('[Gemini] Raw chunk received:', chunk.substring(0, 100) + '...');

      // Try to parse complete JSON objects
      let startIndex = 0;
      while (true) {
        const openBrace = buffer.indexOf('{', startIndex);
        if (openBrace === -1) break;

        let braceCount = 0;
        let endIndex = openBrace;

        // Find matching closing brace
        for (let i = openBrace; i < buffer.length; i++) {
          if (buffer[i] === '{') braceCount++;
          if (buffer[i] === '}') braceCount--;
          if (braceCount === 0) {
            endIndex = i;
            break;
          }
        }

        // If we found a complete JSON object
        if (braceCount === 0) {
          const jsonStr = buffer.substring(openBrace, endIndex + 1);
          try {
            const jsonData = JSON.parse(jsonStr);
            const text = jsonData.candidates?.[0]?.content?.parts?.[0]?.text;

            if (text) {
              console.log('[Gemini] Yielding text chunk:', text.substring(0, 50) + '...');
              yield {
                choices: [{
                  delta: {
                    content: text
                  }
                }]
              };
            }
          } catch (parseError) {
            console.warn('[Gemini] Failed to parse JSON:', jsonStr.substring(0, 100), parseError.message);
          }

          // Remove processed JSON from buffer
          buffer = buffer.substring(endIndex + 1);
          startIndex = 0;
        } else {
          // Incomplete JSON, wait for more data
          break;
        }
      }
    }

    // Log final response for debugging
    console.log('[Gemini] Full response length:', fullResponse.length);
    console.log('[Gemini] Stream completed');

  } catch (error) {
    console.error('[Gemini] Streaming error:', error);
    throw new Error(`Gemini API error: ${error.message}`);
  }
}

/**
 * Get single response from Gemini using REST API (for modifications)
 */
export async function getGeminiResponse(apiKey, modelName, messages, maxTokens = 8192, enableThinking = false) {
  try {
    const actualModelName = getGeminiModelName(modelName);
    const requestBody = convertMessagesToGeminiFormat(messages, enableThinking);

    // Add generation config with lower temperature for modifications
    requestBody.generationConfig = {
      temperature: 0.3, // Lower temperature for more precise modifications
      topP: 0.8,
      topK: 40,
      maxOutputTokens: maxTokens,
      candidateCount: 1,
    };

    const url = `https://generativelanguage.googleapis.com/v1beta/models/${actualModelName}:generateContent?key=${apiKey}`;

    const response = await rateLimitedFetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));

      if (response.status === 429) {
        throw new Error(`Gemini API rate limit exceeded. Please wait a few minutes and try again, or use another AI model like DeepSeek R1.`);
      }

      throw new Error(`Gemini API error: ${response.status} - ${errorData.error?.message || response.statusText}`);
    }

    const data = await response.json();
    const text = data.candidates?.[0]?.content?.parts?.[0]?.text;

    if (!text) {
      throw new Error('No content returned from Gemini API');
    }

    return {
      choices: [{
        message: {
          content: text
        }
      }]
    };

  } catch (error) {
    console.error('Gemini response error:', error);
    throw new Error(`Gemini API error: ${error.message}`);
  }
}

/**
 * Check if model is a Gemini model
 */
export function isGeminiModel(modelName) {
  return modelName && (
    modelName.includes('gemini-1.5-pro') ||
    modelName.includes('gemini-1.5-flash') ||
    modelName.includes('gemini-2.0-flash-exp')
  );
}

/**
 * Get Gemini model capabilities
 */
export function getGeminiCapabilities(modelName) {
  const capabilities = {
    'gemini-1.5-pro': {
      maxTokens: 1000000,
      supportsStreaming: true,
      supportsImages: true,
      supportsCode: true,
      bestFor: ['reasoning', 'complex-tasks', 'analysis']
    },
    'gemini-1.5-flash': {
      maxTokens: 1000000,
      supportsStreaming: true,
      supportsImages: true,
      supportsCode: true,
      bestFor: ['general', 'fast-responses', 'web-development']
    },
    'gemini-2.0-flash-exp': {
      maxTokens: 1000000,
      supportsStreaming: true,
      supportsImages: true,
      supportsCode: true,
      bestFor: ['experimental', 'latest-features', 'advanced-reasoning']
    }
  };

  return capabilities[modelName] || capabilities['gemini-1.5-flash'];
}
