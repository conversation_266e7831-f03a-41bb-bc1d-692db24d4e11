# 🧪 دليل اختبار Gemini في WIDDX DEV

## 🎯 **اختبارات شاملة لضمان عمل Gemini**

### **1. اختبار الإعداد الأساسي**

#### **✅ التحقق من API Key**
```bash
# في ملف .env
GOOGLE_AI_API_KEY=your_actual_api_key_here
```

#### **✅ اختبار الاتصال المباشر**
```bash
node test-gemini.js
```
**النتيجة المتوقعة**: استجابة ناجحة مع HTML

### **2. اختبار الواجهة**

#### **✅ اختبار اختيار النموذج**
1. افتح http://localhost:3000
2. اضغط على أيقونة الإعدادات ⚙️
3. تأكد من ظهور نماذج Gemini:
   - ✅ Gemini 1.5 Pro (مع علامة "New")
   - ✅ Gemini 1.5 Flash (مع علامة "New") 
   - ✅ Gemini 2.0 Flash (مع علامة "Experimental")

#### **✅ اختبار الوصف والفئات**
- تأكد من ظهور الوصف تحت كل نموذج
- تأكد من ظهور الفئة (reasoning, general, experimental)

### **3. اختبار إنشاء محتوى جديد**

#### **✅ اختبار بسيط**
```
الطلب: "Create a simple HTML page with hello world"
النموذج: Gemini 1.5 Flash
النتيجة المتوقعة: 
- بداية فورية للكتابة
- نص يظهر تدريجياً
- HTML كامل وصحيح
```

#### **✅ اختبار معقد**
```
الطلب: "Create a professional business website with navigation, hero section, and contact form"
النموذج: Gemini 1.5 Pro
النتيجة المتوقعة:
- تحليل ذكي للطلب
- كود HTML متقدم
- تصميم احترافي
```

#### **✅ اختبار عربي**
```
الطلب: "أنشئ موقع شركة تقنية باللغة العربية"
النموذج: Gemini 1.5 Pro
النتيجة المتوقعة:
- فهم اللغة العربية
- محتوى باللغة العربية
- اتجاه RTL صحيح
```

### **4. اختبار التعديلات**

#### **✅ تعديل بسيط**
1. أنشئ صفحة HTML بسيطة
2. اطلب تعديل: "Change the background color to blue"
3. **النتيجة المتوقعة**: تغيير لون الخلفية فقط

#### **✅ تعديل معقد**
1. أنشئ موقع كامل
2. اطلب: "Add a new section about our services"
3. **النتيجة المتوقعة**: إضافة قسم جديد بدون تغيير الباقي

#### **✅ تعديل متعدد**
1. اطلب عدة تغييرات: "Change title and add footer"
2. **النتيجة المتوقعة**: تطبيق جميع التغييرات

### **5. اختبار معالجة الأخطاء**

#### **✅ API Key مفقود**
1. احذف `GOOGLE_AI_API_KEY` من `.env`
2. جرب استخدام Gemini
3. **النتيجة المتوقعة**: رسالة خطأ واضحة

#### **✅ API Key خاطئ**
1. ضع API Key خاطئ
2. جرب استخدام Gemini
3. **النتيجة المتوقعة**: رسالة خطأ مفيدة

#### **✅ انقطاع الشبكة**
1. اقطع الإنترنت أثناء الطلب
2. **النتيجة المتوقعة**: رسالة خطأ مناسبة

### **6. اختبار الأداء**

#### **✅ سرعة الاستجابة**
- **Gemini 1.5 Flash**: يجب أن يبدأ خلال 2-3 ثواني
- **Gemini 1.5 Pro**: يجب أن يبدأ خلال 3-5 ثواني
- **Gemini 2.0 Flash**: يجب أن يبدأ خلال 2-4 ثواني

#### **✅ جودة الـ Streaming**
- النص يظهر بسلاسة
- لا توجد توقفات مفاجئة
- الانتهاء واضح مع رسالة النجاح

### **7. اختبار التكامل**

#### **✅ التبديل بين النماذج**
1. استخدم DeepSeek لإنشاء صفحة
2. بدل إلى Gemini للتعديل
3. **النتيجة المتوقعة**: عمل سلس بدون مشاكل

#### **✅ الحفظ والتحميل**
1. أنشئ محتوى بـ Gemini
2. أعد تحميل الصفحة
3. **النتيجة المتوقعة**: المحتوى محفوظ

### **8. اختبار الحالات الحدية**

#### **✅ طلبات طويلة**
```
طلب طويل جداً (أكثر من 1000 كلمة)
النتيجة المتوقعة: معالجة صحيحة أو رسالة حد أقصى
```

#### **✅ طلبات غريبة**
```
طلب: "asdfghjkl"
النتيجة المتوقعة: رد مناسب أو طلب توضيح
```

#### **✅ طلبات متتالية**
1. أرسل طلب
2. أرسل طلب آخر فوراً
3. **النتيجة المتوقعة**: إلغاء الأول وبدء الثاني

## 📊 **نتائج الاختبار المتوقعة**

### **✅ جميع الاختبارات تمر بنجاح**
- ✅ إنشاء محتوى جديد يعمل
- ✅ تعديل المحتوى يعمل  
- ✅ معالجة الأخطاء تعمل
- ✅ الأداء مقبول
- ✅ التكامل سلس

### **🚨 إذا فشل أي اختبار**
1. تحقق من الكونسول للأخطاء
2. تأكد من صحة API Key
3. تحقق من اتصال الإنترنت
4. راجع ملف `server.js` للأخطاء

## 🎉 **تأكيد النجاح**

عند نجاح جميع الاختبارات، ستحصل على:
- **7 نماذج AI** تعمل بسلاسة
- **تجربة موحدة** عبر جميع النماذج
- **أداء ممتاز** وموثوقية عالية
- **دعم كامل** للعربية والإنجليزية

---

**WIDDX DEV مع Gemini - جاهز للإنتاج!** 🚀
