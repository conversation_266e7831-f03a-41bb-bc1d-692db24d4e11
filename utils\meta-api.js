/**
 * Meta Llama API Integration
 * Direct API integration with Llama models
 */

// Rate limiting for Meta API
const rateLimitQueue = [];
const RATE_LIMIT_DELAY = 100; // 100ms between requests

async function rateLimitedFetch(url, options) {
  return new Promise((resolve, reject) => {
    rateLimitQueue.push(async () => {
      try {
        const response = await fetch(url, options);
        resolve(response);
      } catch (error) {
        reject(error);
      }
    });

    if (rateLimitQueue.length === 1) {
      processQueue();
    }
  });
}

async function processQueue() {
  while (rateLimitQueue.length > 0) {
    const request = rateLimitQueue.shift();
    await request();
    if (rateLimitQueue.length > 0) {
      await new Promise(resolve => setTimeout(resolve, RATE_LIMIT_DELAY));
    }
  }
}

/**
 * Check if a model is a Meta Llama model
 */
export function isMetaModel(modelName) {
  const metaModels = [
    'llama-3.3',
    'llama-3.2',
    'llama-3.1',
    'llama-3',
    'llama'
  ];
  
  return metaModels.some(model => 
    modelName.toLowerCase().includes(model)
  );
}

/**
 * Convert messages to Meta Llama format
 */
function convertMessagesToMetaFormat(messages) {
  return messages.map(msg => ({
    role: msg.role,
    content: msg.content
  }));
}

/**
 * Stream response from Meta Llama using REST API
 */
export async function* streamMetaResponse(apiKey, modelName, messages, maxTokens = 8192) {
  try {
    const convertedMessages = convertMessagesToMetaFormat(messages);

    const requestBody = {
      model: modelName,
      messages: convertedMessages,
      stream: true,
      max_tokens: maxTokens,
      temperature: 0.7,
    };

    const url = 'https://api.llama.developer.meta.com/v1/chat/completions';

    console.log('[Meta] Starting stream request to:', url);
    console.log('[Meta] Model:', modelName);
    console.log('[Meta] Messages count:', convertedMessages.length);

    const response = await rateLimitedFetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
      },
      body: JSON.stringify(requestBody)
    });

    console.log('[Meta] Response status:', response.status);

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      console.error('[Meta] API Error:', errorData);

      if (response.status === 429) {
        throw new Error(`Meta API rate limit exceeded. Please wait a few minutes and try again, or use another AI model like DeepSeek.`);
      }

      if (response.status === 401) {
        throw new Error(`Meta API authentication failed. Please check your API key.`);
      }

      throw new Error(`Meta API error: ${response.status} - ${errorData.error?.message || response.statusText}`);
    }

    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let buffer = '';
    let fullResponse = '';

    while (true) {
      const { done, value } = await reader.read();
      
      if (done) {
        console.log('[Meta] Stream completed. Total response length:', fullResponse.length);
        break;
      }

      buffer += decoder.decode(value, { stream: true });
      const lines = buffer.split('\n');
      buffer = lines.pop() || '';

      for (const line of lines) {
        if (line.trim() === '') continue;
        if (line.startsWith('data: ')) {
          const data = line.slice(6);
          if (data === '[DONE]') {
            console.log('[Meta] Received [DONE] signal');
            return;
          }

          try {
            const parsed = JSON.parse(data);
            const content = parsed.choices?.[0]?.delta?.content;
            
            if (content) {
              fullResponse += content;
              yield {
                choices: [{
                  delta: {
                    content: content
                  }
                }]
              };
            }
          } catch (e) {
            console.warn('[Meta] Failed to parse chunk:', data);
          }
        }
      }
    }

  } catch (error) {
    console.error('[Meta] Stream error:', error);
    throw error;
  }
}

/**
 * Get single response from Meta Llama using REST API (for modifications)
 */
export async function getMetaResponse(apiKey, modelName, messages, maxTokens = 8192) {
  try {
    const convertedMessages = convertMessagesToMetaFormat(messages);

    const requestBody = {
      model: modelName,
      messages: convertedMessages,
      stream: false,
      max_tokens: maxTokens,
      temperature: 0.3, // Lower temperature for more precise modifications
    };

    const url = 'https://api.llama.developer.meta.com/v1/chat/completions';

    console.log('[Meta] Making non-stream request to:', url);
    console.log('[Meta] Model:', modelName);

    const response = await rateLimitedFetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
      },
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));

      if (response.status === 429) {
        throw new Error(`Meta API rate limit exceeded. Please wait a few minutes and try again, or use another AI model like DeepSeek.`);
      }

      if (response.status === 401) {
        throw new Error(`Meta API authentication failed. Please check your API key.`);
      }

      throw new Error(`Meta API error: ${response.status} - ${errorData.error?.message || response.statusText}`);
    }

    const data = await response.json();
    const content = data.choices?.[0]?.message?.content;

    if (!content) {
      throw new Error('No content received from Meta API');
    }

    console.log('[Meta] Response received, length:', content.length);
    return content;

  } catch (error) {
    console.error('[Meta] API error:', error);
    throw error;
  }
}
