// Test API request to verify the server is working
async function testAPIRequest() {
  try {
    console.log('🧪 Testing API request...\n');
    
    const response = await fetch('http://localhost:5173/api/ask-ai', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        prompt: 'Create a simple portfolio website for a web developer',
        model: 'deepseek-chat',
        provider: 'auto'
      })
    });
    
    console.log('Response status:', response.status);
    console.log('Response headers:', Object.fromEntries(response.headers.entries()));
    
    if (response.ok) {
      console.log('✅ API request successful!');
      
      // Check if it's a streaming response
      if (response.headers.get('content-type')?.includes('text/plain')) {
        console.log('📡 Streaming response detected');
        
        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let chunks = 0;
        let totalLength = 0;
        
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;
          
          const chunk = decoder.decode(value);
          chunks++;
          totalLength += chunk.length;
          
          if (chunks <= 3) {
            console.log(`Chunk ${chunks}:`, chunk.substring(0, 100) + '...');
          }
        }
        
        console.log(`\n📊 Received ${chunks} chunks, total length: ${totalLength}`);
        
      } else {
        const text = await response.text();
        console.log('Response:', text.substring(0, 200) + '...');
      }
      
    } else {
      console.log('❌ API request failed');
      const errorText = await response.text();
      console.log('Error:', errorText);
    }
    
  } catch (error) {
    console.error('❌ Request error:', error.message);
  }
}

testAPIRequest();
