/* eslint-disable @typescript-eslint/no-explicit-any */
import { useState } from "react";
import { <PERSON>ton } from "../ui/button";
import { Badge } from "../ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "../ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "../ui/tabs";
import { Info, Zap, Brain, Palette, Code, Sparkles } from "lucide-react";
// @ts-expect-error not needed
import { MODELS, PROVIDERS } from "../../../utils/providers";

interface ModelInfoProps {
  open: boolean;
  onClose: () => void;
}

function ModelsInfo({ open, onClose }: ModelInfoProps) {
  const [selectedCategory, setSelectedCategory] = useState("all");

  if (!open) return null;

  const categories = {
    all: { icon: Sparkles, label: "All Models", color: "bg-gradient-to-r from-purple-500 to-pink-500" },
    general: { icon: Zap, label: "General", color: "bg-blue-500" },
    reasoning: { icon: Brain, label: "Reasoning", color: "bg-purple-500" },
    creative: { icon: Palette, label: "Creative", color: "bg-pink-500" },
    coding: { icon: Code, label: "Coding", color: "bg-green-500" },
    experimental: { icon: Sparkles, label: "Experimental", color: "bg-orange-500" }
  };

  const filteredModels = selectedCategory === "all" 
    ? MODELS 
    : MODELS.filter((model: any) => model.category === selectedCategory);

  const getProviderInfo = (providerId: string) => PROVIDERS[providerId];

  const getModelBadges = (model: any) => {
    const badges = [];
    
    if (model.isThinker) badges.push({ label: "Thinker", color: "bg-purple-500" });
    if (model.isExperimental) badges.push({ label: "Experimental", color: "bg-orange-500" });
    if (model.isDirect) badges.push({ label: "Direct API", color: "bg-green-500" });
    
    // Category badge
    const categoryInfo = categories[model.category as keyof typeof categories];
    if (categoryInfo) {
      badges.push({ label: categoryInfo.label, color: categoryInfo.color });
    }
    
    return badges;
  };

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-neutral-900 rounded-xl border border-neutral-700 w-full max-w-6xl max-h-[90vh] overflow-hidden">
        <div className="flex items-center justify-between p-6 border-b border-neutral-700">
          <div className="flex items-center gap-3">
            <Info className="size-6 text-blue-400" />
            <div>
              <h2 className="text-xl font-bold text-white">AI Models Information</h2>
              <p className="text-sm text-neutral-400">12 Advanced AI Models with Direct APIs</p>
            </div>
          </div>
          <Button variant="ghost" size="sm" onClick={onClose}>
            ✕
          </Button>
        </div>

        <div className="p-6">
          <Tabs value={selectedCategory} onValueChange={setSelectedCategory}>
            <TabsList className="grid w-full grid-cols-6 mb-6">
              {Object.entries(categories).map(([key, category]) => {
                const Icon = category.icon;
                const count = key === "all" ? MODELS.length : MODELS.filter((m: any) => m.category === key).length;
                return (
                  <TabsTrigger key={key} value={key} className="flex items-center gap-2">
                    <Icon className="size-4" />
                    {category.label}
                    <Badge variant="secondary" className="ml-1">{count}</Badge>
                  </TabsTrigger>
                );
              })}
            </TabsList>

            <TabsContent value={selectedCategory} className="space-y-4 max-h-[60vh] overflow-y-auto">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {filteredModels.map((model: any) => {
                  const provider = getProviderInfo(model.autoProvider);
                  const badges = getModelBadges(model);
                  
                  return (
                    <Card key={model.value} className="bg-neutral-800 border-neutral-700 hover:border-neutral-600 transition-colors">
                      <CardHeader className="pb-3">
                        <div className="flex items-start justify-between">
                          <div className="flex items-center gap-3">
                            <img 
                              src={`/providers/${model.autoProvider}.svg`} 
                              alt={provider?.name || model.autoProvider}
                              className="size-8 rounded"
                              onError={(e) => {
                                (e.target as HTMLImageElement).style.display = 'none';
                              }}
                            />
                            <div>
                              <CardTitle className="text-white text-sm">{model.label}</CardTitle>
                              <CardDescription className="text-xs text-neutral-400">
                                {provider?.name || model.autoProvider}
                              </CardDescription>
                            </div>
                          </div>
                        </div>
                        
                        <div className="flex flex-wrap gap-1 mt-2">
                          {badges.map((badge, index) => (
                            <Badge 
                              key={index} 
                              className={`text-xs text-white ${badge.color}`}
                            >
                              {badge.label}
                            </Badge>
                          ))}
                        </div>
                      </CardHeader>
                      
                      <CardContent className="pt-0">
                        <p className="text-xs text-neutral-300 mb-3">
                          {model.description}
                        </p>
                        
                        <div className="space-y-2 text-xs">
                          <div className="flex justify-between">
                            <span className="text-neutral-400">Max Tokens:</span>
                            <span className="text-white">{provider?.max_tokens?.toLocaleString() || 'N/A'}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-neutral-400">Model ID:</span>
                            <span className="text-white font-mono text-xs">{model.value}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-neutral-400">API Type:</span>
                            <span className="text-white">{model.isDirect ? 'Direct' : 'Proxy'}</span>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
            </TabsContent>
          </Tabs>
        </div>

        <div className="border-t border-neutral-700 p-4 bg-neutral-950">
          <div className="flex items-center justify-between text-sm">
            <div className="text-neutral-400">
              Total: {MODELS.length} models • All using Direct APIs • No HuggingFace dependency
            </div>
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <div className="size-2 bg-green-500 rounded-full"></div>
                <span className="text-neutral-400">Direct API</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="size-2 bg-purple-500 rounded-full"></div>
                <span className="text-neutral-400">Thinker Model</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default ModelsInfo;
