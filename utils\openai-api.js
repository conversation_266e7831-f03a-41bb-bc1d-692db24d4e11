/**
 * OpenAI API Integration
 * Direct API integration with OpenAI models
 */

// Rate limiting for OpenAI API
const rateLimitQueue = [];
const RATE_LIMIT_DELAY = 100; // 100ms between requests

async function rateLimitedFetch(url, options) {
  return new Promise((resolve, reject) => {
    rateLimitQueue.push(async () => {
      try {
        const response = await fetch(url, options);
        resolve(response);
      } catch (error) {
        reject(error);
      }
    });

    if (rateLimitQueue.length === 1) {
      processQueue();
    }
  });
}

async function processQueue() {
  while (rateLimitQueue.length > 0) {
    const request = rateLimitQueue.shift();
    await request();
    if (rateLimitQueue.length > 0) {
      await new Promise(resolve => setTimeout(resolve, RATE_LIMIT_DELAY));
    }
  }
}

/**
 * Check if a model is an OpenAI model
 */
export function isOpenAIModel(modelName) {
  const openaiModels = [
    'gpt-4o',
    'gpt-4o-mini',
    'gpt-3.5-turbo',
    'gpt-4',
    'gpt-3.5'
  ];
  
  return openaiModels.some(model => 
    modelName.toLowerCase().includes(model)
  );
}

/**
 * Convert messages to OpenAI format
 */
function convertMessagesToOpenAIFormat(messages) {
  return messages.map(msg => ({
    role: msg.role,
    content: msg.content
  }));
}

/**
 * Stream response from OpenAI using REST API
 */
export async function* streamOpenAIResponse(apiKey, modelName, messages, maxTokens = 8192) {
  try {
    const convertedMessages = convertMessagesToOpenAIFormat(messages);

    const requestBody = {
      model: modelName,
      messages: convertedMessages,
      stream: true,
      max_tokens: maxTokens,
      temperature: 0.7,
    };

    const url = 'https://api.openai.com/v1/chat/completions';

    console.log('[OpenAI] Starting stream request to:', url);
    console.log('[OpenAI] Model:', modelName);
    console.log('[OpenAI] Messages count:', convertedMessages.length);

    const response = await rateLimitedFetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
      },
      body: JSON.stringify(requestBody)
    });

    console.log('[OpenAI] Response status:', response.status);

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      console.error('[OpenAI] API Error:', errorData);

      if (response.status === 429) {
        throw new Error(`OpenAI API rate limit exceeded. Please wait a few minutes and try again, or use another AI model like DeepSeek.`);
      }

      if (response.status === 401) {
        throw new Error(`OpenAI API authentication failed. Please check your API key.`);
      }

      throw new Error(`OpenAI API error: ${response.status} - ${errorData.error?.message || response.statusText}`);
    }

    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let buffer = '';
    let fullResponse = '';

    while (true) {
      const { done, value } = await reader.read();
      
      if (done) {
        console.log('[OpenAI] Stream completed. Total response length:', fullResponse.length);
        break;
      }

      buffer += decoder.decode(value, { stream: true });
      const lines = buffer.split('\n');
      buffer = lines.pop() || '';

      for (const line of lines) {
        if (line.trim() === '') continue;
        if (line.startsWith('data: ')) {
          const data = line.slice(6);
          if (data === '[DONE]') {
            console.log('[OpenAI] Received [DONE] signal');
            return;
          }

          try {
            const parsed = JSON.parse(data);
            const content = parsed.choices?.[0]?.delta?.content;
            
            if (content) {
              fullResponse += content;
              yield {
                choices: [{
                  delta: {
                    content: content
                  }
                }]
              };
            }
          } catch (e) {
            console.warn('[OpenAI] Failed to parse chunk:', data);
          }
        }
      }
    }

  } catch (error) {
    console.error('[OpenAI] Stream error:', error);
    throw error;
  }
}

/**
 * Get single response from OpenAI using REST API (for modifications)
 */
export async function getOpenAIResponse(apiKey, modelName, messages, maxTokens = 8192) {
  try {
    const convertedMessages = convertMessagesToOpenAIFormat(messages);

    const requestBody = {
      model: modelName,
      messages: convertedMessages,
      stream: false,
      max_tokens: maxTokens,
      temperature: 0.3, // Lower temperature for more precise modifications
    };

    const url = 'https://api.openai.com/v1/chat/completions';

    console.log('[OpenAI] Making non-stream request to:', url);
    console.log('[OpenAI] Model:', modelName);

    const response = await rateLimitedFetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
      },
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));

      if (response.status === 429) {
        throw new Error(`OpenAI API rate limit exceeded. Please wait a few minutes and try again, or use another AI model like DeepSeek.`);
      }

      if (response.status === 401) {
        throw new Error(`OpenAI API authentication failed. Please check your API key.`);
      }

      throw new Error(`OpenAI API error: ${response.status} - ${errorData.error?.message || response.statusText}`);
    }

    const data = await response.json();
    const content = data.choices?.[0]?.message?.content;

    if (!content) {
      throw new Error('No content received from OpenAI API');
    }

    console.log('[OpenAI] Response received, length:', content.length);
    return content;

  } catch (error) {
    console.error('[OpenAI] API error:', error);
    throw error;
  }
}
