// Test Gemini CSS generation capabilities
import dotenv from 'dotenv';
import { getGeminiResponse, isGeminiModel } from './utils/google-ai.js';

dotenv.config();

async function testGeminiCSS() {
  const apiKey = process.env.GOOGLE_AI_API_KEY;
  
  if (!apiKey) {
    console.error('❌ GOOGLE_AI_API_KEY not found in .env file');
    return;
  }
  
  console.log('🔑 API Key found:', apiKey.substring(0, 10) + '...');
  
  const cssTestPrompts = [
    {
      name: "Modern Card Design",
      prompt: "Create a modern CSS card component with hover effects, shadows, and smooth transitions. Include responsive design."
    },
    {
      name: "Flexbox Layout",
      prompt: "Create a complete HTML page with CSS using Flexbox for a responsive navigation bar and three-column layout."
    },
    {
      name: "CSS Grid Dashboard",
      prompt: "Create a dashboard layout using CSS Grid with header, sidebar, main content, and footer areas. Make it responsive."
    },
    {
      name: "CSS Animations",
      prompt: "Create a loading spinner and button hover animations using pure CSS. Include keyframes and transitions."
    }
  ];

  const models = ['gemini-1.5-flash', 'gemini-1.5-pro'];

  for (const model of models) {
    console.log(`\n🧪 Testing ${model} for CSS capabilities...\n`);
    
    for (const test of cssTestPrompts) {
      console.log(`📝 Test: ${test.name}`);
      
      const messages = [
        {
          role: "system",
          content: `You are WIDDX DEV AI, an expert web developer specialized in creating modern, responsive, and high-performance websites.

CORE PRINCIPLES:
- Always create complete, production-ready HTML files
- Use modern CSS with Flexbox/Grid for layouts
- Ensure mobile-first responsive design
- Include proper semantic HTML structure
- Add accessibility features (ARIA labels, alt text, etc.)
- Optimize for performance and SEO
- Use clean, maintainable code with proper comments

CSS REQUIREMENTS:
- Use modern CSS features (Grid, Flexbox, Custom Properties)
- Include responsive breakpoints
- Add smooth transitions and animations
- Use proper CSS organization and comments
- Ensure cross-browser compatibility
- Follow BEM methodology when appropriate

ALWAYS create something unique, modern, and production-ready that represents the quality of WIDDX hosting services.`
        },
        {
          role: "user",
          content: test.prompt
        }
      ];

      try {
        const response = await getGeminiResponse(apiKey, model, messages, 4096, true);
        const content = response.choices[0]?.message?.content;
        
        if (content) {
          // Analyze CSS content
          const hasThinking = content.includes('<think>') && content.includes('</think>');
          const hasHTML = content.includes('<!DOCTYPE html>') || content.includes('<html');
          const hasCSS = content.includes('<style>') || content.includes('style=') || content.includes('.') && content.includes('{');
          const hasFlexbox = content.toLowerCase().includes('flex') || content.toLowerCase().includes('display: flex');
          const hasGrid = content.toLowerCase().includes('grid') || content.toLowerCase().includes('display: grid');
          const hasResponsive = content.includes('@media') || content.includes('responsive');
          const hasAnimations = content.includes('@keyframes') || content.includes('transition') || content.includes('animation');
          const hasComments = content.includes('/*') || content.includes('//');
          const hasModernCSS = content.includes('var(--') || content.includes('clamp(') || content.includes('min(') || content.includes('max(');
          
          console.log(`   ✅ Response generated (${content.length} chars)`);
          console.log(`   🧠 Thinking: ${hasThinking ? '✅' : '❌'}`);
          console.log(`   📄 HTML: ${hasHTML ? '✅' : '❌'}`);
          console.log(`   🎨 CSS: ${hasCSS ? '✅' : '❌'}`);
          console.log(`   📱 Flexbox: ${hasFlexbox ? '✅' : '❌'}`);
          console.log(`   🔲 Grid: ${hasGrid ? '✅' : '❌'}`);
          console.log(`   📱 Responsive: ${hasResponsive ? '✅' : '❌'}`);
          console.log(`   ✨ Animations: ${hasAnimations ? '✅' : '❌'}`);
          console.log(`   💬 Comments: ${hasComments ? '✅' : '❌'}`);
          console.log(`   🆕 Modern CSS: ${hasModernCSS ? '✅' : '❌'}`);
          
          // Extract and show CSS snippet
          const cssMatch = content.match(/<style>([\s\S]*?)<\/style>/);
          if (cssMatch) {
            const cssCode = cssMatch[1].trim();
            const cssLines = cssCode.split('\n').length;
            console.log(`   📏 CSS Lines: ${cssLines}`);
            
            // Show first few lines of CSS
            const firstLines = cssCode.split('\n').slice(0, 5).join('\n');
            console.log(`   📋 CSS Preview:\n${firstLines}...`);
          }
          
        } else {
          console.log('   ❌ No content returned');
        }
        
      } catch (error) {
        console.log(`   ❌ Error: ${error.message}`);
      }
      
      console.log(''); // Empty line for readability
    }
  }
}

async function testSpecificCSSFeatures() {
  const apiKey = process.env.GOOGLE_AI_API_KEY;
  
  console.log('\n🎯 Testing Specific CSS Features...\n');
  
  const specificTests = [
    {
      feature: "CSS Custom Properties",
      prompt: "Create a dark/light theme toggle using CSS custom properties (CSS variables). Include smooth transitions between themes."
    },
    {
      feature: "CSS Grid Advanced",
      prompt: "Create a complex magazine-style layout using CSS Grid with overlapping elements and different sized grid items."
    },
    {
      feature: "CSS Animations & Keyframes",
      prompt: "Create a collection of loading animations: spinner, pulse, wave, and skeleton loading using CSS keyframes."
    }
  ];

  for (const test of specificTests) {
    console.log(`🔍 Testing: ${test.feature}`);
    
    const messages = [
      {
        role: "system",
        content: `You are WIDDX DEV AI, a CSS expert. Focus on creating advanced, modern CSS with detailed explanations.

REQUIREMENTS:
- Use cutting-edge CSS features
- Include detailed CSS comments explaining each technique
- Ensure browser compatibility notes
- Add performance optimizations
- Use semantic HTML structure
- Include accessibility considerations`
      },
      {
        role: "user",
        content: test.prompt
      }
    ];

    try {
      const response = await getGeminiResponse(apiKey, 'gemini-1.5-pro', messages, 6144, true);
      const content = response.choices[0]?.message?.content;
      
      if (content) {
        const hasAdvancedCSS = content.includes('var(--') || content.includes('@keyframes') || content.includes('grid-template-areas');
        const hasComments = (content.match(/\/\*/g) || []).length;
        const hasAccessibility = content.includes('aria-') || content.includes('focus') || content.includes('screen reader');
        
        console.log(`   ✅ Advanced CSS: ${hasAdvancedCSS ? '✅' : '❌'}`);
        console.log(`   💬 CSS Comments: ${hasComments} found`);
        console.log(`   ♿ Accessibility: ${hasAccessibility ? '✅' : '❌'}`);
        console.log(`   📏 Total Length: ${content.length} characters`);
        
      } else {
        console.log('   ❌ No content returned');
      }
      
    } catch (error) {
      console.log(`   ❌ Error: ${error.message}`);
    }
    
    console.log('');
  }
}

async function runCSSTests() {
  console.log('🎨 Starting Gemini CSS Generation Tests\n');
  
  await testGeminiCSS();
  await testSpecificCSSFeatures();
  
  console.log('✅ All CSS tests completed!');
}

runCSSTests().catch(console.error);
