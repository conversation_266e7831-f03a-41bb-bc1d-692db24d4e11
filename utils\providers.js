export const PROVIDERS = {
  "fireworks-ai": {
    name: "Fireworks AI",
    max_tokens: 131_000,
    id: "fireworks-ai",
  },
  nebius: {
    name: "Nebius AI Studio",
    max_tokens: 131_000,
    id: "nebius",
  },
  sambanova: {
    name: "<PERSON><PERSON><PERSON><PERSON>",
    max_tokens: 32_000,
    id: "sambanova",
  },
  novita: {
    name: "NovitaAI",
    max_tokens: 16_000,
    id: "novita",
  },
  hyperbolic: {
    name: "Hyperbolic",
    max_tokens: 131_000,
    id: "hyperbolic",
  },
  together: {
    name: "Together AI",
    max_tokens: 128_000,
    id: "together",
  },
  google: {
    name: "Google AI Studio",
    max_tokens: 1_000_000,
    id: "google",
  },
};

export const MODELS = [
  {
    value: "deepseek-ai/DeepSeek-V3-0324",
    label: "DeepSeek V3 O324",
    providers: ["fireworks-ai", "nebius", "sambanova", "novita", "hyperbolic"],
    autoProvider: "fireworks-ai",
    description: "Fast and efficient for web development",
    category: "general",
  },
  {
    value: "deepseek-ai/DeepSeek-R1-0528",
    label: "DeepSeek R1 0528",
    providers: ["fireworks-ai", "novita", "hyperbolic", "nebius", "together"],
    autoProvider: "novita",
    isNew: true,
    isThinker: true,
    description: "Advanced reasoning for complex tasks",
    category: "reasoning",
  },
  {
    value: "meta-llama/Llama-3.3-70B-Instruct",
    label: "Llama 3.3 70B",
    providers: ["fireworks-ai", "together", "hyperbolic"],
    autoProvider: "fireworks-ai",
    description: "Excellent for creative web design",
    category: "creative",
  },
  {
    value: "Qwen/Qwen2.5-Coder-32B-Instruct",
    label: "Qwen2.5 Coder 32B",
    providers: ["fireworks-ai", "together"],
    autoProvider: "fireworks-ai",
    description: "Specialized for coding and development",
    category: "coding",
  },
  {
    value: "gemini-1.5-pro",
    label: "Gemini 1.5 Pro",
    providers: ["google"],
    autoProvider: "google",
    description: "Google's most capable model for complex reasoning",
    category: "reasoning",
    isNew: true,
    isThinker: true,
  },
  {
    value: "gemini-1.5-flash",
    label: "Gemini 1.5 Flash",
    providers: ["google"],
    autoProvider: "google",
    description: "Fast and efficient for web development",
    category: "general",
    isNew: true,
    isThinker: true,
  },
  {
    value: "gemini-2.0-flash-exp",
    label: "Gemini 2.0 Flash (Experimental)",
    providers: ["google"],
    autoProvider: "google",
    description: "Latest experimental model with advanced capabilities",
    category: "experimental",
    isNew: true,
    isExperimental: true,
    isThinker: true,
  },
];
