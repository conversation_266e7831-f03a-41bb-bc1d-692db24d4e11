export const PROVIDERS = {
  google: {
    name: "Google AI Studio",
    max_tokens: 1_000_000,
    id: "google",
  },
  deepseek: {
    name: "DeepSeek API",
    max_tokens: 8_192,
    id: "deepseek",
  },
  openai: {
    name: "OpenAI API",
    max_tokens: 128_000,
    id: "openai",
  },
  anthropic: {
    name: "Anthropic API",
    max_tokens: 200_000,
    id: "anthropic",
  },
  meta: {
    name: "Meta Llama API",
    max_tokens: 128_000,
    id: "meta",
  },
  alibaba: {
    name: "Alibaba Cloud API",
    max_tokens: 128_000,
    id: "alibaba",
  },
};

export const MODELS = [
  // DeepSeek Models (Direct API)
  {
    value: "deepseek-chat",
    label: "DeepSeek Chat",
    providers: ["deepseek"],
    autoProvider: "deepseek",
    description: "DeepSeek V3 via direct API - Fast and efficient for web development",
    category: "general",
    isDirect: true,
  },
  {
    value: "deepseek-reasoner",
    label: "DeepSeek Reasoner",
    providers: ["deepseek"],
    autoProvider: "deepseek",
    description: "DeepSeek R1 via direct API - Advanced reasoning for complex tasks",
    category: "reasoning",
    isThinker: true,
    isDirect: true,
  },

  // Google Gemini Models (Direct API)
  {
    value: "gemini-1.5-pro",
    label: "Gemini 1.5 Pro",
    providers: ["google"],
    autoProvider: "google",
    description: "Google's most capable model for complex reasoning and analysis",
    category: "reasoning",
    isThinker: true,
    isDirect: true,
  },
  {
    value: "gemini-1.5-flash",
    label: "Gemini 1.5 Flash",
    providers: ["google"],
    autoProvider: "google",
    description: "Fast and efficient Google model for web development",
    category: "general",
    isThinker: true,
    isDirect: true,
  },
  {
    value: "gemini-2.0-flash-exp",
    label: "Gemini 2.0 Flash (Experimental)",
    providers: ["google"],
    autoProvider: "google",
    description: "Latest experimental Google model with advanced capabilities",
    category: "experimental",
    isExperimental: true,
    isThinker: true,
    isDirect: true,
  },

  // OpenAI Models (Direct API)
  {
    value: "gpt-4o",
    label: "GPT-4o",
    providers: ["openai"],
    autoProvider: "openai",
    description: "OpenAI's most advanced multimodal model",
    category: "reasoning",
    isThinker: true,
    isDirect: true,
  },
  {
    value: "gpt-4o-mini",
    label: "GPT-4o Mini",
    providers: ["openai"],
    autoProvider: "openai",
    description: "Fast and cost-effective OpenAI model",
    category: "general",
    isDirect: true,
  },
  {
    value: "gpt-3.5-turbo",
    label: "GPT-3.5 Turbo",
    providers: ["openai"],
    autoProvider: "openai",
    description: "Reliable and fast OpenAI model for general tasks",
    category: "general",
    isDirect: true,
  },

  // Anthropic Claude Models (Direct API)
  {
    value: "claude-3-5-sonnet-20241022",
    label: "Claude 3.5 Sonnet",
    providers: ["anthropic"],
    autoProvider: "anthropic",
    description: "Anthropic's most capable model for complex reasoning and coding",
    category: "reasoning",
    isThinker: true,
    isDirect: true,
  },
  {
    value: "claude-3-5-haiku-20241022",
    label: "Claude 3.5 Haiku",
    providers: ["anthropic"],
    autoProvider: "anthropic",
    description: "Fast and efficient Anthropic model",
    category: "general",
    isDirect: true,
  },

  // Meta Llama Models (Direct API)
  {
    value: "llama-3.3-70b-instruct",
    label: "Llama 3.3 70B",
    providers: ["meta"],
    autoProvider: "meta",
    description: "Meta's powerful model excellent for creative web design",
    category: "creative",
    isDirect: true,
  },

  // Alibaba Qwen Models (Direct API)
  {
    value: "qwen2.5-coder-32b-instruct",
    label: "Qwen2.5 Coder 32B",
    providers: ["alibaba"],
    autoProvider: "alibaba",
    description: "Alibaba's specialized model for coding and development",
    category: "coding",
    isDirect: true,
  },
];
