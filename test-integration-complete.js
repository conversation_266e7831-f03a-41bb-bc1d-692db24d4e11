// Complete integration test for DeepSeek in WIDDX DEV
import dotenv from 'dotenv';
import { MODELS, PROVIDERS } from './utils/providers.js';
import { isDeepSeekModel } from './utils/deepseek-ai.js';
import { isGeminiModel } from './utils/google-ai.js';
import { analyzePrompt, suggestBestModel } from './utils/ai-helpers.js';

dotenv.config();

function testModelConfiguration() {
  console.log('🔧 Testing Model Configuration:\n');
  
  // Test DeepSeek models in MODELS array
  const deepseekModels = MODELS.filter(m => isDeepSeekModel(m.value));
  console.log(`✅ DeepSeek models found: ${deepseekModels.length}`);
  
  deepseekModels.forEach(model => {
    console.log(`  - ${model.label} (${model.value})`);
    console.log(`    Provider: ${model.autoProvider}`);
    console.log(`    Category: ${model.category}`);
    console.log(`    Thinker: ${model.isThinker ? 'Yes' : 'No'}`);
    console.log(`    Direct API: ${model.isDirect ? 'Yes' : 'No'}`);
    console.log('');
  });
  
  // Test DeepSeek provider
  const deepseekProvider = PROVIDERS.deepseek;
  if (deepseekProvider) {
    console.log(`✅ DeepSeek provider configured:`);
    console.log(`  - Name: ${deepseekProvider.name}`);
    console.log(`  - Max tokens: ${deepseekProvider.max_tokens}`);
    console.log(`  - ID: ${deepseekProvider.id}`);
  } else {
    console.log('❌ DeepSeek provider not found');
  }
  
  console.log('\n' + '='.repeat(50) + '\n');
}

function testModelDetection() {
  console.log('🔍 Testing Model Detection:\n');
  
  const testCases = [
    { model: 'deepseek-chat', expected: true, type: 'DeepSeek Direct' },
    { model: 'deepseek-reasoner', expected: true, type: 'DeepSeek Direct' },
    { model: 'deepseek-ai/DeepSeek-V3-0324', expected: true, type: 'DeepSeek HF' },
    { model: 'deepseek-ai/DeepSeek-R1-0528', expected: true, type: 'DeepSeek HF' },
    { model: 'gemini-1.5-pro', expected: false, type: 'Gemini' },
    { model: 'meta-llama/Llama-3.3-70B-Instruct', expected: false, type: 'Llama' }
  ];
  
  testCases.forEach(({ model, expected, type }) => {
    const isDeepSeek = isDeepSeekModel(model);
    const isGemini = isGeminiModel(model);
    const status = isDeepSeek === expected ? '✅' : '❌';
    
    console.log(`${status} ${model}`);
    console.log(`    Type: ${type}`);
    console.log(`    DeepSeek: ${isDeepSeek ? 'Yes' : 'No'}`);
    console.log(`    Gemini: ${isGemini ? 'Yes' : 'No'}`);
    console.log('');
  });
  
  console.log('='.repeat(50) + '\n');
}

function testModelSelection() {
  console.log('🎯 Testing Model Selection Logic:\n');
  
  const testPrompts = [
    {
      prompt: "Create a simple HTML page for a portfolio",
      expectedCategory: "general",
      description: "General web development"
    },
    {
      prompt: "Build a complex e-commerce system with database integration",
      expectedCategory: "reasoning",
      description: "Complex reasoning task"
    },
    {
      prompt: "Write JavaScript code for form validation",
      expectedCategory: "coding",
      description: "Coding task"
    },
    {
      prompt: "Design a beautiful landing page with animations",
      expectedCategory: "creative",
      description: "Creative design task"
    }
  ];
  
  testPrompts.forEach(({ prompt, expectedCategory, description }) => {
    console.log(`📝 Test: ${description}`);
    console.log(`Prompt: "${prompt}"`);
    
    const analysis = analyzePrompt(prompt);
    console.log(`Analysis: Category=${analysis.category}, Complexity=${analysis.complexity}`);
    
    const suggestedModel = suggestBestModel(analysis, MODELS);
    console.log(`Suggested Model: ${suggestedModel.label} (${suggestedModel.value})`);
    
    const isDeepSeekSuggested = isDeepSeekModel(suggestedModel.value);
    console.log(`DeepSeek suggested: ${isDeepSeekSuggested ? 'Yes' : 'No'}`);
    
    console.log('');
  });
  
  console.log('='.repeat(50) + '\n');
}

function testAPIKeyConfiguration() {
  console.log('🔑 Testing API Key Configuration:\n');
  
  const deepseekKey = process.env.DEEPSEEK_API_KEY;
  const geminiKey = process.env.GOOGLE_AI_API_KEY;
  const hfToken = process.env.HF_TOKEN || process.env.DEFAULT_HF_TOKEN;
  
  console.log(`DeepSeek API Key: ${deepseekKey ? '✅ Configured' : '❌ Missing'}`);
  if (deepseekKey) {
    console.log(`  Length: ${deepseekKey.length} characters`);
    console.log(`  Starts with: ${deepseekKey.substring(0, 10)}...`);
  }
  
  console.log(`Gemini API Key: ${geminiKey ? '✅ Configured' : '❌ Missing'}`);
  console.log(`HuggingFace Token: ${hfToken ? '✅ Configured' : '❌ Missing'}`);
  
  console.log('\n' + '='.repeat(50) + '\n');
}

function testModificationLogic() {
  console.log('🔧 Testing Modification Logic:\n');
  
  // Test which model is selected for modifications
  const modificationModel = MODELS.find(m => m.value === 'deepseek-chat') || MODELS[0];
  
  console.log(`Modification model: ${modificationModel.label}`);
  console.log(`Value: ${modificationModel.value}`);
  console.log(`Is DeepSeek: ${isDeepSeekModel(modificationModel.value) ? 'Yes' : 'No'}`);
  console.log(`Is Direct API: ${modificationModel.isDirect ? 'Yes' : 'No'}`);
  console.log(`Provider: ${modificationModel.autoProvider}`);
  
  if (isDeepSeekModel(modificationModel.value)) {
    console.log('✅ Modifications will use DeepSeek API');
  } else {
    console.log('⚠️ Modifications will use HuggingFace');
  }
  
  console.log('\n' + '='.repeat(50) + '\n');
}

function generateReport() {
  console.log('📊 Integration Report:\n');
  
  const deepseekModels = MODELS.filter(m => isDeepSeekModel(m.value));
  const directDeepSeekModels = deepseekModels.filter(m => m.isDirect);
  const hfDeepSeekModels = deepseekModels.filter(m => !m.isDirect);
  
  console.log(`Total DeepSeek models: ${deepseekModels.length}`);
  console.log(`Direct API models: ${directDeepSeekModels.length}`);
  console.log(`HuggingFace models: ${hfDeepSeekModels.length}`);
  
  console.log('\nDirect API Models:');
  directDeepSeekModels.forEach(m => {
    console.log(`  - ${m.label} (${m.value})`);
  });
  
  console.log('\nHuggingFace Models:');
  hfDeepSeekModels.forEach(m => {
    console.log(`  - ${m.label} (${m.value})`);
  });
  
  const hasDeepSeekKey = !!process.env.DEEPSEEK_API_KEY;
  
  console.log('\n🎯 Integration Status:');
  console.log(`✅ DeepSeek models added: ${deepseekModels.length > 0 ? 'Yes' : 'No'}`);
  console.log(`✅ Direct API models: ${directDeepSeekModels.length > 0 ? 'Yes' : 'No'}`);
  console.log(`✅ Provider configured: ${PROVIDERS.deepseek ? 'Yes' : 'No'}`);
  console.log(`${hasDeepSeekKey ? '✅' : '❌'} API Key configured: ${hasDeepSeekKey ? 'Yes' : 'No'}`);
  
  if (hasDeepSeekKey && directDeepSeekModels.length > 0) {
    console.log('\n🎉 DeepSeek integration is READY!');
    console.log('Next steps:');
    console.log('1. Restart your server: npm start');
    console.log('2. Look for "DeepSeek Chat (Direct API)" and "DeepSeek Reasoner (Direct API)" in the model list');
    console.log('3. Test creating a new project with DeepSeek models');
  } else {
    console.log('\n⚠️ DeepSeek integration needs configuration:');
    if (!hasDeepSeekKey) {
      console.log('- Add DEEPSEEK_API_KEY to your .env file');
      console.log('- Get your API key from: https://platform.deepseek.com/api_keys');
    }
  }
}

async function main() {
  console.log('🚀 WIDDX DEV - DeepSeek Integration Test\n');
  console.log('='.repeat(60) + '\n');
  
  testModelConfiguration();
  testModelDetection();
  testModelSelection();
  testAPIKeyConfiguration();
  testModificationLogic();
  generateReport();
  
  console.log('\n✨ Integration test completed!');
}

main().catch(console.error);
