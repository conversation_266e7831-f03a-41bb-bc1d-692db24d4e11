/**
 * Alibaba Qwen API Integration
 * Direct API integration with Qwen models
 */

// Rate limiting for Alibaba API
const rateLimitQueue = [];
const RATE_LIMIT_DELAY = 100; // 100ms between requests

async function rateLimitedFetch(url, options) {
  return new Promise((resolve, reject) => {
    rateLimitQueue.push(async () => {
      try {
        const response = await fetch(url, options);
        resolve(response);
      } catch (error) {
        reject(error);
      }
    });

    if (rateLimitQueue.length === 1) {
      processQueue();
    }
  });
}

async function processQueue() {
  while (rateLimitQueue.length > 0) {
    const request = rateLimitQueue.shift();
    await request();
    if (rateLimitQueue.length > 0) {
      await new Promise(resolve => setTimeout(resolve, RATE_LIMIT_DELAY));
    }
  }
}

/**
 * Check if a model is an Alibaba Qwen model
 */
export function isAlibabaModel(modelName) {
  const alibabaModels = [
    'qwen2.5',
    'qwen2',
    'qwen',
    'qwen-coder'
  ];
  
  return alibabaModels.some(model => 
    modelName.toLowerCase().includes(model)
  );
}

/**
 * Convert messages to Alibaba Qwen format
 */
function convertMessagesToAlibabaFormat(messages) {
  return messages.map(msg => ({
    role: msg.role,
    content: msg.content
  }));
}

/**
 * Stream response from Alibaba Qwen using REST API
 */
export async function* streamAlibabaResponse(apiKey, modelName, messages, maxTokens = 8192) {
  try {
    const convertedMessages = convertMessagesToAlibabaFormat(messages);

    const requestBody = {
      model: modelName,
      messages: convertedMessages,
      stream: true,
      max_tokens: maxTokens,
      temperature: 0.7,
    };

    const url = 'https://dashscope-intl.aliyuncs.com/compatible-mode/v1/chat/completions';

    console.log('[Alibaba] Starting stream request to:', url);
    console.log('[Alibaba] Model:', modelName);
    console.log('[Alibaba] Messages count:', convertedMessages.length);

    const response = await rateLimitedFetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
      },
      body: JSON.stringify(requestBody)
    });

    console.log('[Alibaba] Response status:', response.status);

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      console.error('[Alibaba] API Error:', errorData);

      if (response.status === 429) {
        throw new Error(`Alibaba API rate limit exceeded. Please wait a few minutes and try again, or use another AI model like DeepSeek.`);
      }

      if (response.status === 401) {
        throw new Error(`Alibaba API authentication failed. Please check your API key.`);
      }

      throw new Error(`Alibaba API error: ${response.status} - ${errorData.error?.message || response.statusText}`);
    }

    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let buffer = '';
    let fullResponse = '';

    while (true) {
      const { done, value } = await reader.read();
      
      if (done) {
        console.log('[Alibaba] Stream completed. Total response length:', fullResponse.length);
        break;
      }

      buffer += decoder.decode(value, { stream: true });
      const lines = buffer.split('\n');
      buffer = lines.pop() || '';

      for (const line of lines) {
        if (line.trim() === '') continue;
        if (line.startsWith('data: ')) {
          const data = line.slice(6);
          if (data === '[DONE]') {
            console.log('[Alibaba] Received [DONE] signal');
            return;
          }

          try {
            const parsed = JSON.parse(data);
            const content = parsed.choices?.[0]?.delta?.content;
            
            if (content) {
              fullResponse += content;
              yield {
                choices: [{
                  delta: {
                    content: content
                  }
                }]
              };
            }
          } catch (e) {
            console.warn('[Alibaba] Failed to parse chunk:', data);
          }
        }
      }
    }

  } catch (error) {
    console.error('[Alibaba] Stream error:', error);
    throw error;
  }
}

/**
 * Get single response from Alibaba Qwen using REST API (for modifications)
 */
export async function getAlibabaResponse(apiKey, modelName, messages, maxTokens = 8192) {
  try {
    const convertedMessages = convertMessagesToAlibabaFormat(messages);

    const requestBody = {
      model: modelName,
      messages: convertedMessages,
      stream: false,
      max_tokens: maxTokens,
      temperature: 0.3, // Lower temperature for more precise modifications
    };

    const url = 'https://dashscope-intl.aliyuncs.com/compatible-mode/v1/chat/completions';

    console.log('[Alibaba] Making non-stream request to:', url);
    console.log('[Alibaba] Model:', modelName);

    const response = await rateLimitedFetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
      },
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));

      if (response.status === 429) {
        throw new Error(`Alibaba API rate limit exceeded. Please wait a few minutes and try again, or use another AI model like DeepSeek.`);
      }

      if (response.status === 401) {
        throw new Error(`Alibaba API authentication failed. Please check your API key.`);
      }

      throw new Error(`Alibaba API error: ${response.status} - ${errorData.error?.message || response.statusText}`);
    }

    const data = await response.json();
    const content = data.choices?.[0]?.message?.content;

    if (!content) {
      throw new Error('No content received from Alibaba API');
    }

    console.log('[Alibaba] Response received, length:', content.length);
    return content;

  } catch (error) {
    console.error('[Alibaba] API error:', error);
    throw error;
  }
}
