// Test Gemini API directly
import dotenv from 'dotenv';

dotenv.config();

async function testGemini() {
  const apiKey = process.env.GOOGLE_AI_API_KEY;
  
  if (!apiKey) {
    console.error('❌ GOOGLE_AI_API_KEY not found in .env file');
    return;
  }
  
  console.log('🔑 API Key found:', apiKey.substring(0, 10) + '...');
  
  const requestBody = {
    contents: [
      {
        parts: [
          {
            text: "Say hello in HTML format"
          }
        ]
      }
    ],
    generationConfig: {
      temperature: 0.7,
      topP: 0.8,
      topK: 40,
      maxOutputTokens: 1000,
    }
  };
  
  const url = `https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=${apiKey}`;
  
  console.log('🚀 Testing Gemini API...');
  console.log('📡 URL:', url.replace(apiKey, 'API_KEY_HIDDEN'));
  console.log('📦 Request body:', JSON.stringify(requestBody, null, 2));
  
  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody)
    });
    
    console.log('📊 Response status:', response.status);
    console.log('📋 Response headers:', Object.fromEntries(response.headers));
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ Error response:', errorText);
      return;
    }
    
    const data = await response.json();
    console.log('✅ Success! Response:', JSON.stringify(data, null, 2));
    
    const text = data.candidates?.[0]?.content?.parts?.[0]?.text;
    if (text) {
      console.log('📝 Generated text:', text);
    } else {
      console.log('⚠️ No text found in response');
    }
    
  } catch (error) {
    console.error('💥 Error:', error.message);
  }
}

testGemini();
