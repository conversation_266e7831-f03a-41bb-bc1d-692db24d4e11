// Test all direct APIs integration
import dotenv from 'dotenv';
import { MODELS, PROVIDERS } from './utils/providers.js';
import { isGeminiModel } from './utils/google-ai.js';
import { isDeepSeekModel } from './utils/deepseek-ai.js';
import { isOpenAIModel } from './utils/openai-api.js';
import { isAnthropicModel } from './utils/anthropic-api.js';
import { isMetaModel } from './utils/meta-api.js';
import { isAlibabaModel } from './utils/alibaba-api.js';

dotenv.config();

function testConfiguration() {
  console.log('🔧 Testing Configuration:\n');
  
  console.log('📊 Providers:');
  Object.entries(PROVIDERS).forEach(([key, provider]) => {
    console.log(`  - ${provider.name} (${key}): ${provider.max_tokens} tokens`);
  });
  
  console.log('\n📋 Models:');
  MODELS.forEach(model => {
    console.log(`  - ${model.label}`);
    console.log(`    Value: ${model.value}`);
    console.log(`    Provider: ${model.autoProvider}`);
    console.log(`    Category: ${model.category}`);
    console.log(`    Direct API: ${model.isDirect ? 'Yes' : 'No'}`);
    console.log(`    Thinker: ${model.isThinker ? 'Yes' : 'No'}`);
    console.log('');
  });
  
  console.log('='.repeat(60) + '\n');
}

function testModelDetection() {
  console.log('🔍 Testing Model Detection:\n');
  
  MODELS.forEach(model => {
    const modelName = model.value;
    const detections = {
      Gemini: isGeminiModel(modelName),
      DeepSeek: isDeepSeekModel(modelName),
      OpenAI: isOpenAIModel(modelName),
      Anthropic: isAnthropicModel(modelName),
      Meta: isMetaModel(modelName),
      Alibaba: isAlibabaModel(modelName)
    };
    
    const detected = Object.entries(detections).filter(([_, value]) => value);
    const detectedTypes = detected.map(([type, _]) => type).join(', ');
    
    console.log(`📝 ${model.label}:`);
    console.log(`    Model: ${modelName}`);
    console.log(`    Detected as: ${detectedTypes || 'Unknown'}`);
    console.log(`    Expected provider: ${model.autoProvider}`);
    
    if (detected.length === 0) {
      console.log(`    ⚠️ WARNING: No detection function matched this model!`);
    } else if (detected.length > 1) {
      console.log(`    ⚠️ WARNING: Multiple detections found!`);
    } else {
      console.log(`    ✅ Detection looks good`);
    }
    console.log('');
  });
  
  console.log('='.repeat(60) + '\n');
}

function testAPIKeys() {
  console.log('🔑 Testing API Keys:\n');
  
  const apiKeys = {
    'Google (Gemini)': process.env.GOOGLE_AI_API_KEY,
    'DeepSeek': process.env.DEEPSEEK_API_KEY,
    'OpenAI': process.env.OPENAI_API_KEY,
    'Anthropic': process.env.ANTHROPIC_API_KEY,
    'Meta': process.env.META_API_KEY,
    'Alibaba': process.env.ALIBABA_API_KEY
  };
  
  Object.entries(apiKeys).forEach(([provider, key]) => {
    const status = key ? '✅ Configured' : '❌ Missing';
    const preview = key ? `${key.substring(0, 10)}...` : 'Not set';
    console.log(`${status} ${provider}: ${preview}`);
  });
  
  console.log('\n='.repeat(60) + '\n');
}

function testProviderMapping() {
  console.log('🔗 Testing Provider Mapping:\n');
  
  const providerModels = {};
  
  MODELS.forEach(model => {
    const provider = model.autoProvider;
    if (!providerModels[provider]) {
      providerModels[provider] = [];
    }
    providerModels[provider].push(model);
  });
  
  Object.entries(providerModels).forEach(([provider, models]) => {
    const providerInfo = PROVIDERS[provider];
    console.log(`📡 ${providerInfo?.name || provider} (${provider}):`);
    console.log(`    Max tokens: ${providerInfo?.max_tokens || 'Unknown'}`);
    console.log(`    Models: ${models.length}`);
    models.forEach(model => {
      console.log(`      - ${model.label} (${model.value})`);
    });
    console.log('');
  });
  
  console.log('='.repeat(60) + '\n');
}

function generateReport() {
  console.log('📊 Migration Report:\n');
  
  const totalModels = MODELS.length;
  const directAPIModels = MODELS.filter(m => m.isDirect).length;
  const thinkerModels = MODELS.filter(m => m.isThinker).length;
  
  console.log(`Total models: ${totalModels}`);
  console.log(`Direct API models: ${directAPIModels}`);
  console.log(`Thinker models: ${thinkerModels}`);
  console.log(`HuggingFace models: 0 (All removed!)`);
  
  console.log('\n📈 Models by category:');
  const categories = {};
  MODELS.forEach(model => {
    const cat = model.category;
    if (!categories[cat]) categories[cat] = 0;
    categories[cat]++;
  });
  
  Object.entries(categories).forEach(([category, count]) => {
    console.log(`  - ${category}: ${count} models`);
  });
  
  console.log('\n🔑 API Keys status:');
  const requiredKeys = ['GOOGLE_AI_API_KEY', 'DEEPSEEK_API_KEY', 'OPENAI_API_KEY', 'ANTHROPIC_API_KEY', 'META_API_KEY', 'ALIBABA_API_KEY'];
  const configuredKeys = requiredKeys.filter(key => process.env[key]);
  
  console.log(`Configured: ${configuredKeys.length}/${requiredKeys.length}`);
  console.log(`Missing: ${requiredKeys.filter(key => !process.env[key]).join(', ')}`);
  
  console.log('\n🎯 Migration Status:');
  if (directAPIModels === totalModels) {
    console.log('✅ Migration COMPLETE: All models use direct APIs');
  } else {
    console.log('⚠️ Migration INCOMPLETE: Some models still use HuggingFace');
  }
  
  if (configuredKeys.length === 0) {
    console.log('❌ No API keys configured - Add keys to .env file');
  } else if (configuredKeys.length < requiredKeys.length) {
    console.log('⚠️ Some API keys missing - Some models won\'t work');
  } else {
    console.log('✅ All API keys configured - All models ready!');
  }
  
  console.log('\n📋 Next steps:');
  console.log('1. Add missing API keys to .env file');
  console.log('2. Restart server: npm start');
  console.log('3. Test models in WIDDX DEV interface');
  console.log('4. Verify all models work correctly');
  
  console.log('\n🎉 HuggingFace dependency removed successfully!');
  console.log('All models now use direct APIs for better performance and reliability.');
}

async function main() {
  console.log('🚀 WIDDX DEV - Direct APIs Migration Test\n');
  console.log('='.repeat(70) + '\n');
  
  testConfiguration();
  testModelDetection();
  testAPIKeys();
  testProviderMapping();
  generateReport();
  
  console.log('\n✨ Migration test completed!');
}

main().catch(console.error);
