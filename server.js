import express from "express";
import path from "path";
import { fileURLToPath } from "url";
import dotenv from "dotenv";
import cookieParser from "cookie-parser";
import {
  createRepo,
  uploadFiles,
  whoAmI,
  spaceInfo,
  fileExists,
} from "@huggingface/hub";
import { InferenceClient } from "@huggingface/inference";
import bodyParser from "body-parser";

import checkUser from "./middlewares/checkUser.js";
import { MODELS, PROVIDERS } from "./utils/providers.js";
import { COLORS } from "./utils/colors.js";
import {
  analyzePrompt,
  suggestBestModel,
  enhancePrompt,
  detectArabic,
  validateHTML
} from "./utils/ai-helpers.js";
import {
  streamGeminiResponse,
  getGeminiResponse,
  isGeminiModel
} from "./utils/google-ai.js";
import {
  streamDeepSeekResponse,
  getDeepSeekResponse,
  isDeepSeekModel
} from "./utils/deepseek-ai.js";

// Load environment variables from .env file
dotenv.config();

const app = express();

const ipAddresses = new Map();

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const PORT = process.env.APP_PORT || 3000;
const REDIRECT_URI =
  process.env.REDIRECT_URI || `http://localhost:${PORT}/auth/login`;
const MAX_REQUESTS_PER_IP = 2;

const SEARCH_START = "<<<<<<< SEARCH";
const DIVIDER = "=======";
const REPLACE_END = ">>>>>>> REPLACE";

app.use(cookieParser());
app.use(bodyParser.json());
app.use(express.static(path.join(__dirname, "dist")));

const getPTag = (repoId) => {
  return `<p style="border-radius: 8px; text-align: center; font-size: 12px; color: #fff; margin-top: 16px;position: fixed; left: 8px; bottom: 8px; z-index: 10; background: rgba(0, 0, 0, 0.8); padding: 4px 8px;">Made with <img src="/logo.svg" alt="WIDDX DEV Logo" style="width: 16px; height: 16px; vertical-align: middle;display:inline-block;margin-right:3px;filter:brightness(0) invert(1);"><a href="#" style="color: #fff;text-decoration: underline;" target="_blank" >WIDDX DEV</a> - 🚀 <a href="#" style="color: #fff;text-decoration: underline;" target="_blank" >Remix</a></p>`;
};

app.get("/api/login", (_req, res) => {
  const redirectUrl = `https://huggingface.co/oauth/authorize?client_id=${process.env.OAUTH_CLIENT_ID}&redirect_uri=${REDIRECT_URI}&response_type=code&scope=openid%20profile%20write-repos%20manage-repos%20inference-api&prompt=consent&state=1234567890`;
  res.status(200).send({
    ok: true,
    redirectUrl,
  });
});
app.get("/auth/login", async (req, res) => {
  const { code } = req.query;

  if (!code) {
    return res.redirect(302, "/");
  }
  const Authorization = `Basic ${Buffer.from(
    `${process.env.OAUTH_CLIENT_ID}:${process.env.OAUTH_CLIENT_SECRET}`
  ).toString("base64")}`;

  const request_auth = await fetch("https://huggingface.co/oauth/token", {
    method: "POST",
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
      Authorization,
    },
    body: new URLSearchParams({
      grant_type: "authorization_code",
      code: code,
      redirect_uri: REDIRECT_URI,
    }),
  });

  const response = await request_auth.json();

  if (!response.access_token) {
    return res.redirect(302, "/");
  }

  res.cookie("hf_token", response.access_token, {
    httpOnly: false,
    secure: true,
    sameSite: "none",
    maxAge: 30 * 24 * 60 * 60 * 1000,
  });

  return res.redirect(302, "/");
});
app.get("/auth/logout", (req, res) => {
  res.clearCookie("hf_token", {
    httpOnly: false,
    secure: true,
    sameSite: "none",
  });
  return res.redirect(302, "/");
});

app.get("/api/@me", checkUser, async (req, res) => {
  let { hf_token } = req.cookies;

  if (process.env.HF_TOKEN && process.env.HF_TOKEN !== "") {
    return res.send({
      preferred_username: "local-use",
      isLocalUse: true,
    });
  }

  try {
    const request_user = await fetch("https://huggingface.co/oauth/userinfo", {
      headers: {
        Authorization: `Bearer ${hf_token}`,
      },
    });

    const user = await request_user.json();
    res.send(user);
  } catch (err) {
    res.clearCookie("hf_token", {
      httpOnly: false,
      secure: true,
      sameSite: "none",
    });
    res.status(401).send({
      ok: false,
      message: err.message,
    });
  }
});

app.post("/api/deploy", checkUser, async (req, res) => {
  const { html, title, path, prompts } = req.body;
  if (!html || (!path && !title)) {
    return res.status(400).send({
      ok: false,
      message: "Missing required fields",
    });
  }

  let { hf_token } = req.cookies;
  if (process.env.HF_TOKEN && process.env.HF_TOKEN !== "") {
    hf_token = process.env.HF_TOKEN;
  }

  try {
    const repo = {
      type: "space",
      name: path ?? "",
    };

    let readme;
    let newHtml = html;

    if (!path || path === "") {
      const { name: username } = await whoAmI({ accessToken: hf_token });
      const newTitle = title
        .toLowerCase()
        .replace(/[^a-z0-9]+/g, "-")
        .split("-")
        .filter(Boolean)
        .join("-")
        .slice(0, 96);

      const repoId = `${username}/${newTitle}`;
      repo.name = repoId;

      await createRepo({
        repo,
        accessToken: hf_token,
      });
      const colorFrom = COLORS[Math.floor(Math.random() * COLORS.length)];
      const colorTo = COLORS[Math.floor(Math.random() * COLORS.length)];
      readme = `---
title: ${newTitle}
emoji: 🐳
colorFrom: ${colorFrom}
colorTo: ${colorTo}
sdk: static
pinned: false
tags:
  - widdx-dev
---

Check out the configuration reference at https://huggingface.co/docs/hub/spaces-config-reference`;
    }

    newHtml = html.replace(/<\/body>/, `${getPTag(repo.name)}</body>`);
    const file = new Blob([newHtml], { type: "text/html" });
    file.name = "index.html"; // Add name property to the Blob

    // create prompt.txt file with all the prompts used, split by new line
    const newPrompts = ``.concat(prompts.map((prompt) => prompt).join("\n"));
    const promptFile = new Blob([newPrompts], { type: "text/plain" });
    promptFile.name = "prompts.txt"; // Add name property to the Blob

    const files = [file, promptFile];
    if (readme) {
      const readmeFile = new Blob([readme], { type: "text/markdown" });
      readmeFile.name = "README.md"; // Add name property to the Blob
      files.push(readmeFile);
    }
    await uploadFiles({
      repo,
      files,
      accessToken: hf_token,
    });
    return res.status(200).send({ ok: true, path: repo.name });
  } catch (err) {
    return res.status(500).send({
      ok: false,
      message: err.message,
    });
  }
});

app.post("/api/ask-ai", async (req, res) => {
  const { prompt, provider, model } = req.body;
  if (!prompt || !model) {
    return res.status(400).send({
      ok: false,
      message: "Missing required fields",
    });
  }

  // Analyze the prompt for better AI selection and enhancement
  const promptAnalysis = analyzePrompt(prompt);
  const isArabic = detectArabic(prompt);

  // Enhance the prompt with context and best practices
  const enhancedPrompt = enhancePrompt(prompt, promptAnalysis, isArabic);

  console.log(`[AI Analysis] Category: ${promptAnalysis.category}, Complexity: ${promptAnalysis.complexity}, Arabic: ${isArabic}`);

  const initialSystemPrompt = `You are WIDDX DEV AI, an expert web developer specialized in creating modern, responsive, and high-performance websites.

CORE REQUIREMENTS:
- Create ONLY HTML, CSS, and JavaScript in a single file
- Use TailwindCSS for styling (include: <script src="https://cdn.tailwindcss.com"></script>)
- Make it fully responsive (mobile-first approach)
- Ensure cross-browser compatibility
- Follow web accessibility standards (WCAG 2.1)
- Optimize for performance and SEO

DESIGN PRINCIPLES:
- Modern, clean, and professional design
- Consistent spacing and typography
- Proper color contrast and readability
- Smooth animations and transitions
- User-friendly navigation and interactions

TECHNICAL STANDARDS:
- Semantic HTML5 structure
- Clean, maintainable CSS
- Vanilla JavaScript (ES6+) when needed
- Proper meta tags for SEO
- Optimized images and assets
- Fast loading times

ICONS & LIBRARIES:
- For icons, use Lucide icons: <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
- For animations, use CSS animations or Tailwind transitions
- Keep external dependencies minimal

OUTPUT FORMAT:
- Single HTML file with embedded CSS and JavaScript
- Well-commented code for maintainability
- Proper indentation and structure
- Include viewport meta tag and charset

ALWAYS create something unique, modern, and production-ready that represents the quality of WIDDX hosting services.`;

  const selectedModel = MODELS.find(
    (m) => m.value === model || m.label === model
  );
  if (!selectedModel) {
    return res.status(400).send({
      ok: false,
      message: "Invalid model selected",
    });
  }
  if (!selectedModel.providers.includes(provider) && provider !== "auto") {
    return res.status(400).send({
      ok: false,
      openSelectProvider: true,
      message: `The selected model does not support the ${provider} provider.`,
    });
  }

  let { hf_token } = req.cookies;
  let token = hf_token;

  if (process.env.HF_TOKEN && process.env.HF_TOKEN !== "") {
    token = process.env.HF_TOKEN;
  }

  // Check if this is a Gemini model
  const isGemini = isGeminiModel(selectedModel.value);
  let geminiApiKey = null;

  // Check if this is a DeepSeek model
  const isDeepSeek = isDeepSeekModel(selectedModel.value);
  let deepseekApiKey = null;

  console.log(`[DEBUG] Model: ${selectedModel.value}, isGemini: ${isGemini}, isDeepSeek: ${isDeepSeek}`);

  if (isGemini) {
    geminiApiKey = process.env.GOOGLE_AI_API_KEY;
    console.log(`[DEBUG] Gemini API Key configured: ${!!geminiApiKey}`);
    if (!geminiApiKey) {
      return res.status(400).send({
        ok: false,
        message: "Google AI API key not configured. Please contact administrator.",
      });
    }
  }

  if (isDeepSeek) {
    deepseekApiKey = process.env.DEEPSEEK_API_KEY;
    console.log(`[DEBUG] DeepSeek API Key configured: ${!!deepseekApiKey}`);
    if (!deepseekApiKey) {
      return res.status(400).send({
        ok: false,
        message: "DeepSeek API key not configured. Please contact administrator.",
      });
    }
  }

  const ip =
    req.headers["x-forwarded-for"]?.split(",")[0].trim() ||
    req.headers["x-real-ip"] ||
    req.socket.remoteAddress ||
    req.ip ||
    "0.0.0.0";

  if (!token) {
    ipAddresses.set(ip, (ipAddresses.get(ip) || 0) + 1);
    if (ipAddresses.get(ip) > MAX_REQUESTS_PER_IP) {
      return res.status(429).send({
        ok: false,
        openLogin: true,
        message: "Log In to continue using the service",
      });
    }

    token = process.env.DEFAULT_HF_TOKEN;
  }

  // Set up response headers for streaming
  res.setHeader("Content-Type", "text/plain");
  res.setHeader("Cache-Control", "no-cache");
  res.setHeader("Connection", "keep-alive");

  const client = new InferenceClient(token);
  let completeResponse = "";

  let TOKENS_USED = prompt?.length;

  const DEFAULT_PROVIDER = PROVIDERS.novita;
  const selectedProvider =
    provider === "auto"
      ? PROVIDERS[selectedModel.autoProvider]
      : PROVIDERS[provider] ?? DEFAULT_PROVIDER;

  if (provider !== "auto" && TOKENS_USED >= selectedProvider.max_tokens) {
    return res.status(400).send({
      ok: false,
      openSelectProvider: true,
      message: `Context is too long. ${selectedProvider.name} allow ${selectedProvider.max_tokens} max tokens.`,
    });
  }

  try {
    let chatCompletion;

    if (isGemini) {
      // Use Google AI for Gemini models with real streaming
      console.log('[DEBUG] Using Gemini real streaming with thinking support');

      try {
        // Use real streaming for Gemini with thinking support
        chatCompletion = streamGeminiResponse(
          geminiApiKey,
          selectedModel.value,
          [
            {
              role: "system",
              content: initialSystemPrompt,
            },
            {
              role: "user",
              content: enhancedPrompt,
            },
          ],
          selectedProvider.max_tokens,
          selectedModel.isThinker // Enable thinking for thinker models
        );

      } catch (error) {
        console.error('[DEBUG] Gemini error:', error);
        if (!res.headersSent) {
          res.status(500).json({ error: error.message });
        } else {
          res.write(`data: ${JSON.stringify({ error: error.message })}\n\n`);
          res.end();
        }
        return;
      }
    } else if (isDeepSeek) {
      // Use DeepSeek API for DeepSeek models with real streaming
      console.log('[DEBUG] Using DeepSeek real streaming with thinking support');

      try {
        // Use real streaming for DeepSeek with thinking support
        chatCompletion = streamDeepSeekResponse(
          deepseekApiKey,
          selectedModel.value,
          [
            {
              role: "system",
              content: initialSystemPrompt,
            },
            {
              role: "user",
              content: enhancedPrompt,
            },
          ],
          selectedProvider.max_tokens,
          selectedModel.isThinker // Enable thinking for thinker models
        );

      } catch (error) {
        console.error('[DEBUG] DeepSeek error:', error);
        if (!res.headersSent) {
          res.status(500).json({ error: error.message });
        } else {
          res.write(`data: ${JSON.stringify({ error: error.message })}\n\n`);
          res.end();
        }
        return;
      }
    } else {
      // Use Hugging Face for other models
      chatCompletion = client.chatCompletionStream({
        model: selectedModel.value,
        provider: selectedProvider.id,
        messages: [
          {
            role: "system",
            content: initialSystemPrompt,
          },
          {
            role: "user",
            content: enhancedPrompt,
          },
        ],
        max_tokens: selectedProvider.max_tokens,
      });
    }

    while (true) {
      const { done, value } = await chatCompletion.next();
      if (done) {
        break;
      }
      const chunk = value.choices[0]?.delta?.content;
      if (chunk) {
        let newChunk = chunk;
        if (!selectedModel?.isThinker) {
          if (provider !== "sambanova") {
            res.write(chunk);
            completeResponse += chunk;

            if (completeResponse.includes("</html>")) {
              break;
            }
          } else {
            let newChunk = chunk;
            if (chunk.includes("</html>")) {
              newChunk = newChunk.replace(/<\/html>[\s\S]*/, "</html>");
            }
            completeResponse += newChunk;
            res.write(newChunk);
            if (newChunk.includes("</html>")) {
              break;
            }
          }
        } else {
          // For thinker models (including Gemini with thinking)
          completeResponse += newChunk;
          res.write(newChunk);

          // Check if we have completed the thinking and HTML
          const lastThinkTagIndex = completeResponse.lastIndexOf("</think>");
          if (lastThinkTagIndex !== -1) {
            const afterLastThinkTag = completeResponse.slice(
              lastThinkTagIndex + "</think>".length
            );
            if (afterLastThinkTag.includes("</html>")) {
              break;
            }
          }

          // Also check for HTML completion without thinking (fallback)
          if (completeResponse.includes("</html>")) {
            break;
          }
        }
      }
    }

    // Validate the generated HTML
    if (completeResponse) {
      const validation = validateHTML(completeResponse);
      if (!validation.isValid) {
        console.log(`[AI Validation] Issues found: ${validation.issues.join(', ')}`);
      }
    }

    // End the response stream
    res.end();
  } catch (error) {
    if (error.message.includes("exceeded your monthly included credits")) {
      return res.status(402).send({
        ok: false,
        openProModal: true,
        message: error.message,
      });
    }
    if (!res.headersSent) {
      res.status(500).send({
        ok: false,
        message:
          error.message || "An error occurred while processing your request.",
      });
    } else {
      // Otherwise end the stream
      res.end();
    }
  }
});

app.put("/api/ask-ai", async (req, res) => {
  const { prompt, html, previousPrompt } = req.body;
  if (!prompt || !html) {
    return res.status(400).send({
      ok: false,
      message: "Missing required fields",
    });
  }
  const followUpSystemPrompt = `You are WIDDX DEV AI, an expert web developer modifying existing HTML files with precision and expertise.

MODIFICATION PRINCIPLES:
- Analyze the current code structure and maintain consistency
- Apply modern web development best practices
- Ensure responsive design and accessibility
- Optimize performance while making changes
- Maintain the existing design language unless specifically requested to change it

SEARCH/REPLACE FORMAT RULES:
You MUST use the exact SEARCH/REPLACE block format. Do NOT output the entire file.

1. Start with ${SEARCH_START}
2. Provide the EXACT lines from the current code (including whitespace and indentation)
3. Use ${DIVIDER} to separate search from replacement
4. Provide the new lines that should replace the original lines
5. End with ${REPLACE_END}
6. Use multiple blocks for changes in different parts of the file
7. For insertions: provide the line before insertion point in SEARCH, include that line plus new lines in REPLACE
8. For deletions: provide lines to delete in SEARCH, leave REPLACE empty
9. CRITICAL: SEARCH block must match EXACTLY - every space, tab, and character

QUALITY STANDARDS:
- Maintain code formatting and indentation
- Follow existing naming conventions
- Ensure cross-browser compatibility
- Add proper comments for complex changes
- Test logic before suggesting changes
- Consider mobile responsiveness in all modifications

Always explain the changes briefly before the blocks, focusing on the technical reasoning and benefits.
Example Modifying Code:
\`\`\`
Some explanation...
${SEARCH_START}
    <h1>Old Title</h1>
${DIVIDER}
    <h1>New Title</h1>
${REPLACE_END}
${SEARCH_START}
  </body>
${DIVIDER}
    <script>console.log("Added script");</script>
  </body>
${REPLACE_END}
\`\`\`
Example Deleting Code:
\`\`\`
Removing the paragraph...
${SEARCH_START}
  <p>This paragraph will be deleted.</p>
${DIVIDER}
${REPLACE_END}
\`\`\``;

  // Use DeepSeek Chat (Direct API) for modifications to avoid thinker models and get better performance
  const selectedModel = MODELS.find(m => m.value === 'deepseek-chat') || MODELS[0];

  let { hf_token } = req.cookies;
  let token = hf_token;

  if (process.env.HF_TOKEN && process.env.HF_TOKEN !== "") {
    token = process.env.HF_TOKEN;
  }

  const ip =
    req.headers["x-forwarded-for"]?.split(",")[0].trim() ||
    req.headers["x-real-ip"] ||
    req.socket.remoteAddress ||
    req.ip ||
    "0.0.0.0";

  if (!token) {
    ipAddresses.set(ip, (ipAddresses.get(ip) || 0) + 1);
    if (ipAddresses.get(ip) > MAX_REQUESTS_PER_IP) {
      return res.status(429).send({
        ok: false,
        openLogin: true,
        message: "Log In to continue using the service",
      });
    }

    token = process.env.DEFAULT_HF_TOKEN;
  }

  const client = new InferenceClient(token);

  const selectedProvider = PROVIDERS[selectedModel.autoProvider];

  try {
    let response;

    // Check if this is a Gemini model
    const isGemini = isGeminiModel(selectedModel.value);
    // Check if this is a DeepSeek model
    const isDeepSeek = isDeepSeekModel(selectedModel.value);

    if (isGemini) {
      const geminiApiKey = process.env.GOOGLE_AI_API_KEY;
      if (!geminiApiKey) {
        return res.status(400).send({
          ok: false,
          message: "Google AI API key not configured. Please contact administrator.",
        });
      }

      response = await getGeminiResponse(
        geminiApiKey,
        selectedModel.value,
        [
          {
            role: "system",
            content: followUpSystemPrompt,
          },
          {
            role: "user",
            content: previousPrompt
              ? previousPrompt
              : "You are modifying the HTML file based on the user's request.",
          },
          {
            role: "assistant",
            content: `The current code is: \n\`\`\`html\n${html}\n\`\`\``,
          },
          {
            role: "user",
            content: prompt,
          },
        ],
        selectedProvider.max_tokens,
        false // Disable thinking for modifications to get clean SEARCH/REPLACE blocks
      );
    } else if (isDeepSeek) {
      const deepseekApiKey = process.env.DEEPSEEK_API_KEY;
      if (!deepseekApiKey) {
        return res.status(400).send({
          ok: false,
          message: "DeepSeek API key not configured. Please contact administrator.",
        });
      }

      response = await getDeepSeekResponse(
        deepseekApiKey,
        selectedModel.value,
        [
          {
            role: "system",
            content: followUpSystemPrompt,
          },
          {
            role: "user",
            content: previousPrompt
              ? previousPrompt
              : "You are modifying the HTML file based on the user's request.",
          },
          {
            role: "assistant",
            content: `The current code is: \n\`\`\`html\n${html}\n\`\`\``,
          },
          {
            role: "user",
            content: prompt,
          },
        ],
        selectedProvider.max_tokens,
        false // Disable thinking for modifications to get clean SEARCH/REPLACE blocks
      );
    } else {
      response = await client.chatCompletion({
        model: selectedModel.value,
        provider: selectedProvider.id,
        messages: [
          {
            role: "system",
            content: followUpSystemPrompt,
          },
          {
            role: "user",
            content: previousPrompt
              ? previousPrompt
              : "You are modifying the HTML file based on the user's request.",
          },
          {
            role: "assistant",
            content: `The current code is: \n\`\`\`html\n${html}\n\`\`\``,
          },
          {
            role: "user",
            content: prompt,
          },
        ],
        ...(selectedProvider.id !== "sambanova"
          ? {
              max_tokens: selectedProvider.max_tokens,
            }
          : {}),
      });
    }

    // Extract content based on response type
    let chunk;
    if (isGemini || isDeepSeek) {
      // For direct API responses, content is already a string
      chunk = response;
    } else {
      // For HuggingFace responses, extract from choices
      chunk = response.choices[0]?.message?.content;
    }

    // TO DO: handle the case where there are multiple SEARCH/REPLACE blocks
    if (!chunk) {
      return res.status(400).send({
        ok: false,
        message: "No content returned from the model",
      });
    }

    if (chunk) {
      let newHtml = html;
      // array of arrays to hold updated lines (start and end line numbers)
      const updatedLines = [];

      // Find all search/replace blocks in the chunk
      let position = 0;
      let moreBlocks = true;

      while (moreBlocks) {
        const searchStartIndex = chunk.indexOf(SEARCH_START, position);
        if (searchStartIndex === -1) {
          moreBlocks = false;
          continue;
        }

        const dividerIndex = chunk.indexOf(DIVIDER, searchStartIndex);
        if (dividerIndex === -1) {
          moreBlocks = false;
          continue;
        }

        const replaceEndIndex = chunk.indexOf(REPLACE_END, dividerIndex);
        if (replaceEndIndex === -1) {
          moreBlocks = false;
          continue;
        }

        // Extract the search and replace blocks
        const searchBlock = chunk.substring(
          searchStartIndex + SEARCH_START.length,
          dividerIndex
        );
        const replaceBlock = chunk.substring(
          dividerIndex + DIVIDER.length,
          replaceEndIndex
        );

        // Apply the replacement
        if (searchBlock.trim() === "") {
          // Inserting at the beginning
          newHtml = `${replaceBlock}\n${newHtml}`;

          // Track first line as updated
          updatedLines.push([1, replaceBlock.split("\n").length]);
        } else {
          // Find the position of the search block in the HTML
          const blockPosition = newHtml.indexOf(searchBlock);
          if (blockPosition !== -1) {
            // Count lines before the search block
            const beforeText = newHtml.substring(0, blockPosition);
            const startLineNumber = beforeText.split("\n").length;

            // Count lines in search and replace blocks
            const replaceLines = replaceBlock.split("\n").length;

            // Calculate end line (start + length of replaced content)
            const endLineNumber = startLineNumber + replaceLines - 1;

            // Track the line numbers that were updated
            updatedLines.push([startLineNumber, endLineNumber]);

            // Perform the replacement
            newHtml = newHtml.replace(searchBlock, replaceBlock);
          }
        }

        // Move position to after this block to find the next one
        position = replaceEndIndex + REPLACE_END.length;
      }

      return res.status(200).send({
        ok: true,
        html: newHtml,
        updatedLines,
      });
    } else {
      return res.status(400).send({
        ok: false,
        message: "No content returned from the model",
      });
    }
  } catch (error) {
    if (error.message.includes("exceeded your monthly included credits")) {
      return res.status(402).send({
        ok: false,
        openProModal: true,
        message: error.message,
      });
    }
    if (!res.headersSent) {
      res.status(500).send({
        ok: false,
        message:
          error.message || "An error occurred while processing your request.",
      });
    }
  }
});

app.get("/api/remix/:username/:repo", async (req, res) => {
  const { username, repo } = req.params;
  const { hf_token } = req.cookies;

  let token = hf_token || process.env.DEFAULT_HF_TOKEN;

  if (process.env.HF_TOKEN && process.env.HF_TOKEN !== "") {
    token = process.env.HF_TOKEN;
  }

  const repoId = `${username}/${repo}`;

  const url = `https://huggingface.co/spaces/${repoId}/raw/main/index.html`;
  try {
    const space = await spaceInfo({
      name: repoId,
      accessToken: token,
      additionalFields: ["author"],
    });

    if (!space || space.sdk !== "static" || space.private) {
      return res.status(404).send({
        ok: false,
        message: "Space not found",
      });
    }

    const response = await fetch(url);
    if (!response.ok) {
      return res.status(404).send({
        ok: false,
        message: "Space not found",
      });
    }
    let html = await response.text();
    // remove the last p tag including this url https://enzostvs-deepsite.hf.space
    html = html.replace(getPTag(repoId), "");

    let user = null;

    if (token) {
      const request_user = await fetch(
        "https://huggingface.co/oauth/userinfo",
        {
          headers: {
            Authorization: `Bearer ${hf_token}`,
          },
        }
      )
        .then((res) => res.json())
        .catch(() => null);

      user = request_user;
    }

    res.status(200).send({
      ok: true,
      html,
      isOwner: space.author === user?.preferred_username,
      path: repoId,
    });
  } catch (error) {
    return res.status(500).send({
      ok: false,
      message: error.message,
    });
  }
});
app.get("*", (_req, res) => {
  res.sendFile(path.join(__dirname, "dist", "index.html"));
});

app.listen(PORT, () => {
  console.log(`Server is running on port ${PORT}`);
});
