// AI Helper functions for WIDDX DEV

/**
 * Analyzes the user prompt to determine the best AI model and approach
 */
export function analyzePrompt(prompt) {
  const promptLower = prompt.toLowerCase();
  
  // Keywords for different categories
  const categories = {
    creative: ['design', 'beautiful', 'modern', 'stylish', 'aesthetic', 'creative', 'artistic', 'visual'],
    coding: ['function', 'javascript', 'code', 'script', 'programming', 'logic', 'algorithm'],
    ecommerce: ['shop', 'store', 'product', 'cart', 'buy', 'sell', 'payment', 'checkout'],
    business: ['company', 'corporate', 'professional', 'business', 'service', 'about'],
    portfolio: ['portfolio', 'showcase', 'gallery', 'work', 'project', 'resume'],
    blog: ['blog', 'article', 'post', 'news', 'content', 'writing'],
    landing: ['landing', 'hero', 'cta', 'conversion', 'signup', 'subscribe']
  };

  // Complexity indicators
  const complexityIndicators = {
    simple: ['simple', 'basic', 'minimal', 'clean'],
    medium: ['interactive', 'dynamic', 'responsive', 'modern'],
    complex: ['advanced', 'complex', 'sophisticated', 'enterprise', 'full-featured']
  };

  // Determine category
  let detectedCategory = 'general';
  let maxMatches = 0;
  
  for (const [category, keywords] of Object.entries(categories)) {
    const matches = keywords.filter(keyword => promptLower.includes(keyword)).length;
    if (matches > maxMatches) {
      maxMatches = matches;
      detectedCategory = category;
    }
  }

  // Determine complexity
  let complexity = 'medium';
  for (const [level, indicators] of Object.entries(complexityIndicators)) {
    if (indicators.some(indicator => promptLower.includes(indicator))) {
      complexity = level;
      break;
    }
  }

  return {
    category: detectedCategory,
    complexity,
    isModification: promptLower.includes('change') || promptLower.includes('modify') || promptLower.includes('update'),
    needsInteractivity: promptLower.includes('click') || promptLower.includes('hover') || promptLower.includes('interactive'),
    needsAnimation: promptLower.includes('animate') || promptLower.includes('transition') || promptLower.includes('smooth')
  };
}

/**
 * Suggests the best AI model based on prompt analysis
 */
export function suggestBestModel(promptAnalysis, availableModels) {
  const { category, complexity, needsInteractivity } = promptAnalysis;

  // Model preferences by category
  const modelPreferences = {
    creative: ['meta-llama/Llama-3.3-70B-Instruct', 'gemini-1.5-pro', 'deepseek-chat', 'deepseek-ai/DeepSeek-V3-0324'],
    coding: ['Qwen/Qwen2.5-Coder-32B-Instruct', 'deepseek-chat', 'gemini-1.5-flash', 'deepseek-ai/DeepSeek-V3-0324'],
    reasoning: ['deepseek-reasoner', 'gemini-1.5-pro', 'deepseek-ai/DeepSeek-R1-0528'],
    general: ['deepseek-chat', 'gemini-1.5-flash', 'deepseek-ai/DeepSeek-V3-0324', 'meta-llama/Llama-3.3-70B-Instruct'],
    experimental: ['gemini-2.0-flash-exp']
  };

  // For complex tasks or reasoning, prefer R1
  if (complexity === 'complex' || needsInteractivity) {
    return availableModels.find(m => m.category === 'reasoning') || availableModels[0];
  }

  // Find preferred model for category
  const preferences = modelPreferences[category] || modelPreferences.general;
  
  for (const preferredValue of preferences) {
    const model = availableModels.find(m => m.value === preferredValue);
    if (model) return model;
  }

  return availableModels[0]; // Fallback
}

/**
 * Enhances the user prompt with context and best practices
 */
export function enhancePrompt(originalPrompt, promptAnalysis, isArabic = false) {
  const { category, complexity, needsAnimation, needsInteractivity } = promptAnalysis;

  let enhancedPrompt = originalPrompt;

  // Add category-specific enhancements
  const enhancements = {
    ecommerce: isArabic ? 
      '\n\nتأكد من إضافة: عرض المنتجات، سلة التسوق، أزرار الشراء، وتصميم متجاوب للهواتف.' :
      '\n\nEnsure to include: product showcase, shopping cart functionality, buy buttons, and mobile-responsive design.',
    
    business: isArabic ?
      '\n\nركز على: التصميم المهني، معلومات الشركة، خدمات واضحة، ونموذج اتصال.' :
      '\n\nFocus on: professional design, company information, clear services, and contact form.',
    
    portfolio: isArabic ?
      '\n\nأضف: معرض الأعمال، السيرة الذاتية، المهارات، ونموذج التواصل.' :
      '\n\nInclude: work gallery, bio section, skills showcase, and contact form.',
    
    blog: isArabic ?
      '\n\nتأكد من: تخطيط المقالات، شريط جانبي، أرشيف، وتصميم قابل للقراءة.' :
      '\n\nEnsure: article layout, sidebar, archive, and readable typography.'
  };

  if (enhancements[category]) {
    enhancedPrompt += enhancements[category];
  }

  // Add performance and accessibility reminders
  if (complexity !== 'simple') {
    enhancedPrompt += isArabic ?
      '\n\nمتطلبات إضافية: تحسين السرعة، إمكانية الوصول، وتوافق المتصفحات.' :
      '\n\nAdditional requirements: optimize for speed, ensure accessibility, and cross-browser compatibility.';
  }

  if (needsAnimation) {
    enhancedPrompt += isArabic ?
      '\n\nأضف حركات سلسة ومؤثرات بصرية جذابة باستخدام CSS.' :
      '\n\nAdd smooth animations and engaging visual effects using CSS.';
  }

  if (needsInteractivity) {
    enhancedPrompt += isArabic ?
      '\n\nأضف تفاعلية باستخدام JavaScript الحديث.' :
      '\n\nAdd interactivity using modern JavaScript.';
  }

  return enhancedPrompt;
}

/**
 * Detects if the prompt is in Arabic
 */
export function detectArabic(text) {
  const arabicRegex = /[\u0600-\u06FF]/;
  return arabicRegex.test(text);
}

/**
 * Validates HTML output for common issues
 */
export function validateHTML(html) {
  const issues = [];

  // Check for required elements
  if (!html.includes('<!DOCTYPE html>')) {
    issues.push('Missing DOCTYPE declaration');
  }
  
  if (!html.includes('<meta name="viewport"')) {
    issues.push('Missing viewport meta tag');
  }
  
  if (!html.includes('charset=')) {
    issues.push('Missing charset declaration');
  }

  // Check for TailwindCSS
  if (!html.includes('tailwindcss.com')) {
    issues.push('TailwindCSS not included');
  }

  // Check for basic structure
  if (!html.includes('<title>')) {
    issues.push('Missing title tag');
  }

  return {
    isValid: issues.length === 0,
    issues
  };
}
