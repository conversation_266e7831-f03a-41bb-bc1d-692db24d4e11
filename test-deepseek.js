// Test DeepSeek API integration
import dotenv from 'dotenv';
import { streamDeepSeekResponse, getDeepSeekResponse, isDeepSeekModel } from './utils/deepseek-ai.js';

dotenv.config();

async function testDeepSeekAPI() {
  const apiKey = process.env.DEEPSEEK_API_KEY;
  
  if (!apiKey) {
    console.error('❌ DEEPSEEK_API_KEY not found in .env file');
    console.log('📝 Please add your DeepSeek API key to .env file:');
    console.log('   DEEPSEEK_API_KEY=your_api_key_here');
    console.log('🔗 Get your API key from: https://platform.deepseek.com/api_keys');
    return;
  }
  
  console.log('🔑 API Key found:', apiKey.substring(0, 10) + '...');
  
  const testPrompt = "Create a simple HTML page for a modern portfolio website with a hero section, about section, and contact form. Make it responsive and use modern CSS.";
  
  const messages = [
    {
      role: "system",
      content: `You are WIDDX DEV AI, an expert web developer specialized in creating modern, responsive, and high-performance websites.

Create complete, production-ready HTML files with:
- Modern, clean design
- Responsive layout (mobile-first)
- Semantic HTML5 structure
- CSS3 with modern features (flexbox, grid, animations)
- Interactive JavaScript when needed
- Accessibility best practices
- Performance optimization
- Cross-browser compatibility

Always include:
- Proper DOCTYPE and meta tags
- Responsive viewport meta tag
- Modern CSS reset/normalize
- Clean, readable code structure
- Comments for complex sections

Focus on creating websites that represent the high quality and professionalism of WIDDX hosting services.`
    },
    {
      role: "user",
      content: testPrompt
    }
  ];

  console.log('\n🧪 Testing DeepSeek Chat (deepseek-chat)...');
  await testModel('deepseek-chat', apiKey, messages);
  
  console.log('\n🧪 Testing DeepSeek Reasoner (deepseek-reasoner)...');
  await testModel('deepseek-reasoner', apiKey, messages);
}

async function testModel(modelName, apiKey, messages) {
  try {
    console.log(`\n📡 Testing ${modelName}:`);
    console.log('- Model detection:', isDeepSeekModel(modelName) ? '✅ DeepSeek' : '❌ Not DeepSeek');
    
    // Test streaming response
    console.log('- Testing streaming...');
    let streamContent = '';
    let chunkCount = 0;
    
    const startTime = Date.now();
    
    for await (const chunk of streamDeepSeekResponse(apiKey, modelName, messages, 4000, true)) {
      const content = chunk.choices?.[0]?.delta?.content;
      if (content) {
        streamContent += content;
        chunkCount++;
        if (chunkCount <= 3) {
          process.stdout.write(content);
        } else if (chunkCount === 4) {
          process.stdout.write('...');
        }
      }
    }
    
    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;
    
    console.log(`\n\n✅ Streaming test completed:`);
    console.log(`- Duration: ${duration.toFixed(2)}s`);
    console.log(`- Chunks received: ${chunkCount}`);
    console.log(`- Total length: ${streamContent.length} characters`);
    console.log(`- Contains HTML: ${streamContent.includes('<!DOCTYPE html>') ? '✅' : '❌'}`);
    console.log(`- Contains CSS: ${streamContent.includes('<style>') || streamContent.includes('css') ? '✅' : '❌'}`);
    console.log(`- Contains thinking: ${streamContent.includes('<think>') ? '✅' : '❌'}`);
    
    // Test non-streaming response
    console.log('\n- Testing non-streaming...');
    const nonStreamStart = Date.now();
    const nonStreamContent = await getDeepSeekResponse(apiKey, modelName, messages, 2000, false);
    const nonStreamEnd = Date.now();
    const nonStreamDuration = (nonStreamEnd - nonStreamStart) / 1000;
    
    console.log(`✅ Non-streaming test completed:`);
    console.log(`- Duration: ${nonStreamDuration.toFixed(2)}s`);
    console.log(`- Length: ${nonStreamContent.length} characters`);
    console.log(`- Contains HTML: ${nonStreamContent.includes('<!DOCTYPE html>') ? '✅' : '❌'}`);
    
  } catch (error) {
    console.error(`❌ Test failed for ${modelName}:`, error.message);
  }
}

async function testModelDetection() {
  console.log('\n🔍 Testing model detection:');
  
  const testModels = [
    'deepseek-chat',
    'deepseek-reasoner',
    'deepseek-ai/DeepSeek-V3-0324',
    'deepseek-ai/DeepSeek-R1-0528',
    'gemini-1.5-pro',
    'meta-llama/Llama-3.3-70B-Instruct'
  ];
  
  testModels.forEach(model => {
    const isDeepSeek = isDeepSeekModel(model);
    console.log(`- ${model}: ${isDeepSeek ? '✅ DeepSeek' : '❌ Not DeepSeek'}`);
  });
}

async function main() {
  console.log('🚀 DeepSeek API Integration Test\n');
  
  await testModelDetection();
  await testDeepSeekAPI();
  
  console.log('\n✨ Test completed!');
  console.log('\n📋 Next steps:');
  console.log('1. Make sure DEEPSEEK_API_KEY is set in your .env file');
  console.log('2. Restart your server: npm start');
  console.log('3. Try the new DeepSeek models in WIDDX DEV');
  console.log('4. Look for "DeepSeek Chat (Direct API)" and "DeepSeek Reasoner (Direct API)" in the model list');
}

main().catch(console.error);
