/**
 * DeepSeek AI API Integration
 * Direct API integration with DeepSeek models
 */

// Rate limiting for DeepSeek API
const rateLimitQueue = [];
const RATE_LIMIT_DELAY = 100; // 100ms between requests

async function rateLimitedFetch(url, options) {
  return new Promise((resolve, reject) => {
    rateLimitQueue.push(async () => {
      try {
        const response = await fetch(url, options);
        resolve(response);
      } catch (error) {
        reject(error);
      }
    });

    if (rateLimitQueue.length === 1) {
      processQueue();
    }
  });
}

async function processQueue() {
  while (rateLimitQueue.length > 0) {
    const request = rateLimitQueue.shift();
    await request();
    if (rateLimitQueue.length > 0) {
      await new Promise(resolve => setTimeout(resolve, RATE_LIMIT_DELAY));
    }
  }
}

/**
 * Check if a model is a DeepSeek model
 */
export function isDeepSeekModel(modelName) {
  const deepseekModels = [
    'deepseek-chat',
    'deepseek-reasoner',
    'deepseek-coder',
    'deepseek-v3',
    'deepseek-r1'
  ];
  
  return deepseekModels.some(model => 
    modelName.toLowerCase().includes(model) || 
    modelName.includes('deepseek-ai/')
  );
}

/**
 * Get the actual DeepSeek model name for API calls
 */
function getDeepSeekModelName(modelName) {
  // Map WIDDX model names to DeepSeek API model names
  const modelMapping = {
    'deepseek-ai/DeepSeek-V3-0324': 'deepseek-chat',
    'deepseek-ai/DeepSeek-R1-0528': 'deepseek-reasoner',
    'deepseek-v3': 'deepseek-chat',
    'deepseek-r1': 'deepseek-reasoner'
  };

  return modelMapping[modelName] || modelName;
}

/**
 * Convert messages to DeepSeek format
 */
function convertMessagesToDeepSeekFormat(messages, enableThinking = false) {
  const convertedMessages = messages.map(msg => ({
    role: msg.role,
    content: msg.content
  }));

  // Add thinking instructions for reasoning models
  if (enableThinking) {
    const systemMessage = convertedMessages.find(msg => msg.role === 'system');
    if (systemMessage) {
      systemMessage.content += `\n\nFor complex tasks, you can use <think></think> tags to show your reasoning process before providing the final answer.`;
    }
  }

  return convertedMessages;
}

/**
 * Stream response from DeepSeek using REST API
 */
export async function* streamDeepSeekResponse(apiKey, modelName, messages, maxTokens = 8192, enableThinking = true) {
  try {
    const actualModelName = getDeepSeekModelName(modelName);
    const convertedMessages = convertMessagesToDeepSeekFormat(messages, enableThinking);

    const requestBody = {
      model: actualModelName,
      messages: convertedMessages,
      stream: true,
      max_tokens: maxTokens,
      temperature: 0.7,
      top_p: 0.8,
    };

    const url = 'https://api.deepseek.com/chat/completions';

    console.log('[DeepSeek] Starting stream request to:', url);
    console.log('[DeepSeek] Model:', actualModelName);
    console.log('[DeepSeek] Messages count:', convertedMessages.length);

    const response = await rateLimitedFetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
      },
      body: JSON.stringify(requestBody)
    });

    console.log('[DeepSeek] Response status:', response.status);

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      console.error('[DeepSeek] API Error:', errorData);

      if (response.status === 429) {
        throw new Error(`DeepSeek API rate limit exceeded. Please wait a few minutes and try again, or use another AI model like Gemini.`);
      }

      if (response.status === 401) {
        throw new Error(`DeepSeek API authentication failed. Please check your API key.`);
      }

      throw new Error(`DeepSeek API error: ${response.status} - ${errorData.error?.message || response.statusText}`);
    }

    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let buffer = '';
    let fullResponse = '';

    while (true) {
      const { done, value } = await reader.read();
      
      if (done) {
        console.log('[DeepSeek] Stream completed. Total response length:', fullResponse.length);
        break;
      }

      buffer += decoder.decode(value, { stream: true });
      const lines = buffer.split('\n');
      buffer = lines.pop() || '';

      for (const line of lines) {
        if (line.trim() === '') continue;
        if (line.startsWith('data: ')) {
          const data = line.slice(6);
          if (data === '[DONE]') {
            console.log('[DeepSeek] Received [DONE] signal');
            return;
          }

          try {
            const parsed = JSON.parse(data);
            const content = parsed.choices?.[0]?.delta?.content;
            
            if (content) {
              fullResponse += content;
              yield {
                choices: [{
                  delta: {
                    content: content
                  }
                }]
              };
            }
          } catch (e) {
            console.warn('[DeepSeek] Failed to parse chunk:', data);
          }
        }
      }
    }

  } catch (error) {
    console.error('[DeepSeek] Stream error:', error);
    throw error;
  }
}

/**
 * Get single response from DeepSeek using REST API (for modifications)
 */
export async function getDeepSeekResponse(apiKey, modelName, messages, maxTokens = 8192, enableThinking = false) {
  try {
    const actualModelName = getDeepSeekModelName(modelName);
    const convertedMessages = convertMessagesToDeepSeekFormat(messages, enableThinking);

    const requestBody = {
      model: actualModelName,
      messages: convertedMessages,
      stream: false,
      max_tokens: maxTokens,
      temperature: 0.3, // Lower temperature for more precise modifications
      top_p: 0.8,
    };

    const url = 'https://api.deepseek.com/chat/completions';

    console.log('[DeepSeek] Making non-stream request to:', url);
    console.log('[DeepSeek] Model:', actualModelName);

    const response = await rateLimitedFetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
      },
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));

      if (response.status === 429) {
        throw new Error(`DeepSeek API rate limit exceeded. Please wait a few minutes and try again, or use another AI model like Gemini.`);
      }

      if (response.status === 401) {
        throw new Error(`DeepSeek API authentication failed. Please check your API key.`);
      }

      throw new Error(`DeepSeek API error: ${response.status} - ${errorData.error?.message || response.statusText}`);
    }

    const data = await response.json();
    const content = data.choices?.[0]?.message?.content;

    if (!content) {
      throw new Error('No content received from DeepSeek API');
    }

    console.log('[DeepSeek] Response received, length:', content.length);
    return content;

  } catch (error) {
    console.error('[DeepSeek] API error:', error);
    throw error;
  }
}
