/**
 * Anthropic Claude API Integration
 * Direct API integration with Claude models
 */

// Rate limiting for Anthropic API
const rateLimitQueue = [];
const RATE_LIMIT_DELAY = 100; // 100ms between requests

async function rateLimitedFetch(url, options) {
  return new Promise((resolve, reject) => {
    rateLimitQueue.push(async () => {
      try {
        const response = await fetch(url, options);
        resolve(response);
      } catch (error) {
        reject(error);
      }
    });

    if (rateLimitQueue.length === 1) {
      processQueue();
    }
  });
}

async function processQueue() {
  while (rateLimitQueue.length > 0) {
    const request = rateLimitQueue.shift();
    await request();
    if (rateLimitQueue.length > 0) {
      await new Promise(resolve => setTimeout(resolve, RATE_LIMIT_DELAY));
    }
  }
}

/**
 * Check if a model is an Anthropic model
 */
export function isAnthropicModel(modelName) {
  const anthropicModels = [
    'claude-3-5-sonnet',
    'claude-3-5-haiku',
    'claude-3-opus',
    'claude-3-sonnet',
    'claude-3-haiku',
    'claude'
  ];
  
  return anthropicModels.some(model => 
    modelName.toLowerCase().includes(model)
  );
}

/**
 * Convert messages to Anthropic format
 */
function convertMessagesToAnthropicFormat(messages) {
  const systemMessage = messages.find(msg => msg.role === 'system');
  const conversationMessages = messages.filter(msg => msg.role !== 'system');

  const anthropicMessages = conversationMessages.map(msg => ({
    role: msg.role === 'assistant' ? 'assistant' : 'user',
    content: msg.content
  }));

  return {
    system: systemMessage?.content || '',
    messages: anthropicMessages
  };
}

/**
 * Stream response from Anthropic using REST API
 */
export async function* streamAnthropicResponse(apiKey, modelName, messages, maxTokens = 8192) {
  try {
    const { system, messages: anthropicMessages } = convertMessagesToAnthropicFormat(messages);

    const requestBody = {
      model: modelName,
      max_tokens: maxTokens,
      temperature: 0.7,
      system: system,
      messages: anthropicMessages,
      stream: true
    };

    const url = 'https://api.anthropic.com/v1/messages';

    console.log('[Anthropic] Starting stream request to:', url);
    console.log('[Anthropic] Model:', modelName);
    console.log('[Anthropic] Messages count:', anthropicMessages.length);

    const response = await rateLimitedFetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': apiKey,
        'anthropic-version': '2023-06-01',
      },
      body: JSON.stringify(requestBody)
    });

    console.log('[Anthropic] Response status:', response.status);

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      console.error('[Anthropic] API Error:', errorData);

      if (response.status === 429) {
        throw new Error(`Anthropic API rate limit exceeded. Please wait a few minutes and try again, or use another AI model like DeepSeek.`);
      }

      if (response.status === 401) {
        throw new Error(`Anthropic API authentication failed. Please check your API key.`);
      }

      throw new Error(`Anthropic API error: ${response.status} - ${errorData.error?.message || response.statusText}`);
    }

    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let buffer = '';
    let fullResponse = '';

    while (true) {
      const { done, value } = await reader.read();
      
      if (done) {
        console.log('[Anthropic] Stream completed. Total response length:', fullResponse.length);
        break;
      }

      buffer += decoder.decode(value, { stream: true });
      const lines = buffer.split('\n');
      buffer = lines.pop() || '';

      for (const line of lines) {
        if (line.trim() === '') continue;
        if (line.startsWith('data: ')) {
          const data = line.slice(6);
          
          try {
            const parsed = JSON.parse(data);
            
            if (parsed.type === 'content_block_delta') {
              const content = parsed.delta?.text;
              if (content) {
                fullResponse += content;
                yield {
                  choices: [{
                    delta: {
                      content: content
                    }
                  }]
                };
              }
            } else if (parsed.type === 'message_stop') {
              console.log('[Anthropic] Received message_stop signal');
              return;
            }
          } catch (e) {
            console.warn('[Anthropic] Failed to parse chunk:', data);
          }
        }
      }
    }

  } catch (error) {
    console.error('[Anthropic] Stream error:', error);
    throw error;
  }
}

/**
 * Get single response from Anthropic using REST API (for modifications)
 */
export async function getAnthropicResponse(apiKey, modelName, messages, maxTokens = 8192) {
  try {
    const { system, messages: anthropicMessages } = convertMessagesToAnthropicFormat(messages);

    const requestBody = {
      model: modelName,
      max_tokens: maxTokens,
      temperature: 0.3, // Lower temperature for more precise modifications
      system: system,
      messages: anthropicMessages,
      stream: false
    };

    const url = 'https://api.anthropic.com/v1/messages';

    console.log('[Anthropic] Making non-stream request to:', url);
    console.log('[Anthropic] Model:', modelName);

    const response = await rateLimitedFetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': apiKey,
        'anthropic-version': '2023-06-01',
      },
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));

      if (response.status === 429) {
        throw new Error(`Anthropic API rate limit exceeded. Please wait a few minutes and try again, or use another AI model like DeepSeek.`);
      }

      if (response.status === 401) {
        throw new Error(`Anthropic API authentication failed. Please check your API key.`);
      }

      throw new Error(`Anthropic API error: ${response.status} - ${errorData.error?.message || response.statusText}`);
    }

    const data = await response.json();
    const content = data.content?.[0]?.text;

    if (!content) {
      throw new Error('No content received from Anthropic API');
    }

    console.log('[Anthropic] Response received, length:', content.length);
    return content;

  } catch (error) {
    console.error('[Anthropic] API error:', error);
    throw error;
  }
}
