// Test Gemini API with thinking support
import dotenv from 'dotenv';
import { streamGeminiResponse, getGeminiResponse, isGeminiModel } from './utils/google-ai.js';

dotenv.config();

async function testGeminiThinking() {
  const apiKey = process.env.GOOGLE_AI_API_KEY;
  
  if (!apiKey) {
    console.error('❌ GOOGLE_AI_API_KEY not found in .env file');
    return;
  }
  
  console.log('🔑 API Key found:', apiKey.substring(0, 10) + '...');
  
  const testPrompt = "Create a simple HTML page for a modern portfolio website with a hero section, about section, and contact form. Make it responsive and use modern CSS.";
  
  const messages = [
    {
      role: "system",
      content: `You are WIDDX DEV AI, an expert web developer specialized in creating modern, responsive, and high-performance websites.

CORE PRINCIPLES:
- Always create complete, production-ready HTML files
- Use modern CSS with Flexbox/Grid for layouts
- Ensure mobile-first responsive design
- Include proper semantic HTML structure
- Add accessibility features (ARIA labels, alt text, etc.)
- Optimize for performance and SEO
- Use clean, maintainable code with proper comments

ALWAYS create something unique, modern, and production-ready that represents the quality of WIDDX hosting services.`
    },
    {
      role: "user",
      content: testPrompt
    }
  ];

  console.log('\n🧠 Testing Gemini with thinking support...');
  
  try {
    // Test streaming with thinking
    console.log('\n📡 Testing streaming response with thinking:');
    const streamGenerator = streamGeminiResponse(apiKey, 'gemini-1.5-flash', messages, 8192, true);
    
    let fullResponse = '';
    let thinkingContent = '';
    let isInThinking = false;
    
    for await (const chunk of streamGenerator) {
      const content = chunk.choices[0]?.delta?.content;
      if (content) {
        fullResponse += content;
        
        // Track thinking content
        if (content.includes('<think>')) {
          isInThinking = true;
          console.log('\n🤔 Thinking started...');
        }
        
        if (isInThinking && !content.includes('</think>')) {
          thinkingContent += content;
          process.stdout.write('.');
        }
        
        if (content.includes('</think>')) {
          isInThinking = false;
          console.log('\n✅ Thinking completed!');
          console.log('💭 Thinking content length:', thinkingContent.length);
        }
        
        if (!isInThinking && fullResponse.includes('</think>')) {
          process.stdout.write(content);
        }
      }
    }
    
    console.log('\n\n📊 Test Results:');
    console.log('- Full response length:', fullResponse.length);
    console.log('- Contains thinking tags:', fullResponse.includes('<think>') && fullResponse.includes('</think>'));
    console.log('- Contains HTML:', fullResponse.includes('<!DOCTYPE html>'));
    console.log('- Contains CSS:', fullResponse.includes('<style>') || fullResponse.includes('style='));
    
    // Test non-streaming response
    console.log('\n📄 Testing non-streaming response:');
    const response = await getGeminiResponse(apiKey, 'gemini-1.5-flash', messages, 4096, true);
    const content = response.choices[0]?.message?.content;
    
    console.log('- Response length:', content?.length || 0);
    console.log('- Contains thinking:', content?.includes('<think>') && content?.includes('</think>'));
    console.log('- Contains HTML:', content?.includes('<!DOCTYPE html>'));
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

async function testModelDetection() {
  console.log('\n🔍 Testing model detection:');
  
  const testModels = [
    'gemini-1.5-pro',
    'gemini-1.5-flash', 
    'gemini-2.0-flash-exp',
    'deepseek-ai/DeepSeek-V3-0324',
    'meta-llama/Llama-3.3-70B-Instruct'
  ];
  
  testModels.forEach(model => {
    const isGemini = isGeminiModel(model);
    console.log(`- ${model}: ${isGemini ? '✅ Gemini' : '❌ Not Gemini'}`);
  });
}

async function runTests() {
  console.log('🚀 Starting Gemini Thinking Tests\n');
  
  await testModelDetection();
  await testGeminiThinking();
  
  console.log('\n✅ All tests completed!');
}

runTests().catch(console.error);
